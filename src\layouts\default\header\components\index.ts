/*
 * @Description:
 * @Autor: yst
 * @Date: 2024-10-22 08:22:45
 * @LastEditors: yst
 * @LastEditTime: 2024-10-22 08:39:09
 */
import {createAsyncComponent} from '/@/utils/factory/createAsyncComponent';
import FullScreen from './FullScreen.vue';

export const UserDropDown = createAsyncComponent(() => import('./user-dropdown/index.vue'), {
  loading: true,
});
export const Feedback = createAsyncComponent(() => import('./feedback.vue'));

export const LayoutBreadcrumb = createAsyncComponent(() => import('./Breadcrumb.vue'));

export const Notify = createAsyncComponent(() => import('./notify/index.vue'));

export const ErrorAction = createAsyncComponent(() => import('./ErrorAction.vue'));

export const LockScreen = createAsyncComponent(() => import('./LockScreen.vue'));

export const GlobalWebMenu = createAsyncComponent(() => import('./GlobalWebMenu.vue'));

export { FullScreen };
