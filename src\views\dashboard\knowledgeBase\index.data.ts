import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';
export const documentManagementColumns: BasicColumn[] = [
  {
    title: '',
    width: 34,
    dataIndex: 'fileType',
    slots: { customRender: 'typeIcons' },
  },
  {
    title: '名称',
    width: 120,
    dataIndex: 'fileName',
    // edit: true,
    editRow: true,
    editComponent: 'Input',
  },
  // {
  //   title: '文件类型',
  //   width: 120,
  //   dataIndex: 'fileType',
  // },
  {
    title: '创建时间',
    width: 120,
    dataIndex: 'createTime',
    format: (text) => {
      return dayjs(text).format('YYYY-MM-DD');
    },
  },
  {
    title: '创建人',
    width: 120,
    dataIndex: 'createBy_dictText',
  },
  // {
  //   title: '文件大小',
  //   width: 120,
  //   dataIndex: 'fileSize',
  // },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    customRender: ({ text }) => {
      if (text) {
        let unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        let index = Math.floor(Math.log(text) / Math.log(1000));
        let size = text / Math.pow(1000, index);
        // @ts-ignore
        size = size.toFixed(2); //保留的小数位数
        return size + unitArr[index];
      } else {
        return '-';
      }
    },
  },
];
export const iconFile = 'intelligentToolbox';
export const iconFileFile = `${iconFile}-file`;

export const columns = [
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    width: 260,
    ellipsis: true,
  },
  {
    title: '创建时间',
    width: 120,
    dataIndex: 'createTime',
    key: 'createTime',
    format: (text) => {
      return dayjs(text).format('YYYY-MM-DD');
    },
  },
  {
    title: '创建人',
    width: 120,
    dataIndex: 'createBy_dictText',
    key:'createBy_dictText',
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    width: 120,
    key: 'fileSize',
    customRender: ({ text }) => {
      if (text) {
        let unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        let index = Math.floor(Math.log(text) / Math.log(1000));
        let size = text / Math.pow(1000, index);
        // @ts-ignore
        size = size.toFixed(2); //保留的小数位数
        return size + unitArr[index];
      } else {
        return '-';
      }
    },
  },
];

export const miniColumns = [
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '创建者',
    dataIndex: 'realname',
    width: 100,
    key: 'realname',
  },
  {
    title: '修改时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 150,
    customRender: ({ text }) => {
      if (text) {
        return dayjs(text).format('YYYY-MM-DD');
      } else {
        return '-';
      }
    },
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    customRender: ({ text }) => {
      if (text) {
        let unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        let index = Math.floor(Math.log(text) / Math.log(1000));
        let size = text / Math.pow(1000, index);
        // @ts-ignore
        size = size.toFixed(2); //保留的小数位数
        return size + unitArr[index];
      } else {
        return '-';
      }
    },
  },
];