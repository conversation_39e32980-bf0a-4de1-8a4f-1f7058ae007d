<template>
  <div class="scheduleBox">
    <div class="smallTit">
      <span style="font-weight: bold; font-size: 22px"> 我的日程 </span>
      <div class="more" @click="handleMore">查看更多 <right-outlined /> </div>
    </div>
    <div class="calendarBox">
      <div class="riliB">
        <div>
          <div class="flex justify-between items-center">
            <div class="">
              <div class="flex justify-center items-center text-color-[#ffffff]">
                <span class="text-24px cursor-pointer" @click="handleCheckMonth('last')">&blacktriangleleft;</span>
                <span class="text-16px" style="font-weight: bold">{{ dayjs(currentMonths).format('YYYY年M月') }}</span>
                <span class="text-24px cursor-pointer" @click="handleCheckMonth('next')">&blacktriangleright;</span>
              </div>
            </div>
            <div style="text-align: right">
              <a-button size="small" type="primary" ghost style="margin-right: 10px" @click="handleToToday">今天</a-button>
              <a-select v-model:value="currentWeek" size="small" class="w-86px" :options="options" @select="handleSelectWeeks" />
            </div>
          </div>
        </div>
        <div class="flex" style="margin-top: 8px">
          <div style="background-color: #f7f8fa" v-for="(item, index) in weeks" :key="index" class="flex-1 text-center pt-16px ml-8px"
            >{{ item }}
          </div>
        </div>
        <div class="flex">
          <div
            v-for="(item, index) in dayList"
            :key="index"
            class="flex-1 text-center cursor-pointer"
            @click="handleCheckDay(item.date)"
            style="position: relative"
          >
            <p class="pb-10px ml-8px flex justify-center" style="background-color: #f7f8fa">
              <span class="w-32px h-32px rounded-4px text-20px" :class="[currentDay === item.date ? 'selectdate' : '']">{{ item.dayOfMonth }}</span>
            </p>
            <!-- 遮罩层 -->
            <div :class="[currentDay === item.date ? 'showsdate' : '']"> </div>
            <p class="mb-0px pt-0px pb-10px">
              <a-badge
                style="display: flex; align-items: flex-start; justify-content: center; padding-left: 12px"
                class="flex"
                color="#2db7f5"
                v-if="item.existToDone.toString() === 'true'"
              />
              <span v-else class="h-4px block"></span>
            </p>
          </div>
        </div>
      </div>
      <div class="richengList">
        <div v-if="scheduleList.length > 0">
          <div class="richengLi" v-for="(item, index) in scheduleList" :key="index">
            <div class="richengLeft">
              <div class="img">
                <img :src="item.type == '3' ? richeng01 : item.type == '4' ? richeng03 : ''" alt="" />
              </div>
              <div class="wenzi">
                <div class="title">{{ item.title }}</div>
                <div class="decSmall" v-if="item.type == '3'">
                  <div class="spanBox">日程</div>
                  <span> {{ item?.startTime?.split(' ')[0] }}{{ item?.endTime ? ` - ${item?.endTime?.split(' ')[0]}` : '' }} </span>
                </div>
                <div class="decSmall" v-if="item.type == '4'">
                  <div class="spanBoxG">日志</div>
                  <span> {{ item?.startTime?.split(' ')[0] }}{{ item?.endTime ? ` - ${item?.endTime?.split(' ')[0]}` : '' }} </span>
                </div>
              </div>
            </div>
            <div class="richengRight" @click="clickRcInfo(item)"> 详情 </div>
          </div>
        </div>
        <div v-else class="flex justify-center pt-80px">
          <span>
            今日暂无日程，
            <span class="text-color-[#717376] cursor-pointer" @click="handleAddSchedule">
              添加日程<PlusCircleOutlined style="margin-left: 5px" />
            </span>
          </span>
        </div>
      </div>
      <div class="addRicheng" @click="handleAddSchedule"> <PlusCircleOutlined /> 添加日程 </div>
    </div>
    <ScheduleModal @register="registerModal" @success="handleSuccess" />
    <DetailModal @register="register" @success="handleSuccess" />
  </div>
</template>
<script setup lang="ts">
  import { RightOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import DetailModal from '@/views/dashboard/calendarDetails/DetailModal.vue';
  import { useRouter } from 'vue-router';
  import richeng01 from '/@/assets/images/homeImg/richeng01.png';
  import richeng03 from '/@/assets/images/homeImg/richeng03.png';
  import { useModal } from '/@/components/Modal';
  import useWeeksInYearMonth from '/@/hooks/web/useWeeksInYearMonth';
  import { WeekDaysItem } from '/@/api/dashboard/model/scheduleModel';
  import { getSysScheduleWeekDays } from '/@/api/dashboard/schedule';
  import { PlusOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
  import { getList, getDetailById } from '../../calendarDetails/calendarDetails.api';
  import ScheduleModal from './schedule/ScheduleModal.vue';
  import { getListApi, getStatisticsCountApi, getworkLogListApi } from '/@/views/dashboard/HomePage/components/schedule/schedule.api';
  const router = useRouter();
  const currentMonths = ref<string>(dayjs().format('YYYY-MM'));
  const [register, { openModal: openDetail }] = useModal();
  const statisticalData = ref({
    pendingApproval: 0,
    applyFor: 0,
    daySchedule: 0,
    weekSchedule: 0,
  });
  const [registerModal, { openModal }] = useModal();
  //   import { tabsType } from './schedule/schedule.data';
  const weekNum = [
    {
      value: 1,
      label: '第一周',
    },
    {
      value: 2,
      label: '第二周',
    },
    {
      value: 3,
      label: '第三周',
    },
    {
      value: 4,
      label: '第四周',
    },
    {
      value: 5,
      label: '第五周',
    },
    {
      value: 6,
      label: '第六周',
    },
  ];
  const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const dayList = ref<WeekDaysItem[]>([]);
  const currentDay = ref<string>('');
  let scheduleList = ref([]);
  let workLogList = ref([]);
  const { weeksNumber, currentWeeksNumber } = useWeeksInYearMonth(currentMonths);
  const options = ref(weekNum.slice(0, weeksNumber.value));
  const currentWeek = ref(currentWeeksNumber.value);
  //   const richengList = ref([
  //     // {
  //     //   id: 1,
  //     //   title: '参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议',
  //     //   time: '08:30 - 09:30',
  //     //   type: '1',
  //     // },
  //     // {
  //     //   id: 2,
  //     //   title: '参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议',
  //     //   time: '08:30 - 09:30',
  //     //   type: '2',
  //     // },
  //     // {
  //     //   id: 3,
  //     //   title: '参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议',
  //     //   time: '08:30 - 09:30',
  //     //   type: '1',
  //     // },
  //     // {
  //     //   id: 4,
  //     //   title: '参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议',
  //     //   time: '08:30 - 09:30',
  //     //   type: '2',
  //     // },
  //     // {
  //     //   id: 5,
  //     //   title: '参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议参加早班班前会议',
  //     //   time: '08:30 - 09:30',
  //     //   type: '1',
  //     // },
  //   ]);
  //   const activeKey = ref(tabsType[0].key);
  const handleCheckDay = (date: string) => {
    currentDay.value = date;
    getScheduleList();
    getWorkLogList();
  };
  const getScheduleList = async () => {
    const res = await getListApi({
      // page: 1,
      // pageSize: 100,
      startTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 00:00:00`,
      endTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 23:59:59`,
    });

    scheduleList.value = [...res];
  };
  const getWorkLogList = async () => {
    const res = await getworkLogListApi({
      startTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 00:00:00`,
      endTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 23:59:59`,
      self: true,
    });
    workLogList.value = [...res];
  };
  const getScheduleWeekDays = async () => {
    const res = await getSysScheduleWeekDays({
      year: dayjs(currentMonths.value).format('YYYY'),
      month: dayjs(currentMonths.value).format('M'),
      week: currentWeek.value,
    });
    dayList.value = res;
    currentDay.value =
      res.find(({ date }) => {
        return date === dayjs().format('YYYY-MM-DD');
      })?.date || res[0].date;
    getScheduleList();
    getWorkLogList();
  };
  //   const getWeekCount = async () => {
  //     const res = await getWeekCountApi();
  //     // statisticalData
  //   };

  const getStatisticsCount = async () => {
    const res = await getStatisticsCountApi({});
    statisticalData.value = res;
  };
  const handleCheckMonth = (type) => {
    currentWeek.value = 1;
    if (type === 'last') {
      currentMonths.value = dayjs(currentMonths.value).subtract(1, 'month').format('YYYY-MM');
    } else {
      currentMonths.value = dayjs(currentMonths.value).add(1, 'month').format('YYYY-MM');
    }
    options.value = [];
    for (let i = 0; i < weeksNumber.value; i++) {
      options.value.push(weekNum[i]);
    }
    getScheduleWeekDays();
  };
  const handleToToday = () => {
    currentWeek.value = currentWeeksNumber.value;
    currentMonths.value = dayjs().format('YYYY-MM-DD');
    getScheduleWeekDays();
  };
  const handleSelectWeeks = (value: any) => {
    currentWeek.value = value;
    getScheduleWeekDays();
  };
  const handleAddSchedule = () => {
    openModal(true, {
      isUpdate: false,
      //   activeKey: unref(activeKey),
    });
  };
  const handleSuccess = () => {
    getScheduleWeekDays();
    getWeekCount();
    getStatisticsCount();
  };
  onMounted(() => {
    getScheduleWeekDays();
    // getWeekCount();
    getStatisticsCount();
  });
  // 日程详情
  async function clickRcInfo(item) {
    // 检查 item 的 type 是否为 3
    if (item.type === 4) {
      // 查找数组中与 item 的 title 和 item 都匹配的对象
      let found = workLogList.value.find((res) => res.title === item.title && res.content === item.item);
      // 如果找到了匹配的项
      if (found) {
        found.type = 2;
        // let temp = await getDetailById({ id: found.id }, true);

        let record = found;
        openDetail(true, {
          record,
          isUpdate: true,
        });

        // 将找到的对象的 id 赋值给 item 的新属性
        // item.id = found.id;
        console.log('找到匹配项的 ID:', item.matchedId);
      } else {
        console.log('未找到匹配项');
        return 0;
      }
    } else {
      console.log('item 的 type 不是 4');

      // if (!item.id) {
      //   return 0;
      // }
      let temp = await getDetailById({ id: item.id });

      // let res = await getList({ search: item.title, sendTime: currentDay.value });
      // res.records[0]
      let record = temp;
      openDetail(true, {
        record,
        isUpdate: true,
      });
    }
    // router.push(`/calendarDetails/${currentDay.value}`);
  }
  //   点击我的日常跳转更多
  function handleMore() {
    router.push('/calendarDetails');
  }
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 688px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
      .more {
        float: right;
        font-size: 16px;
        color: #717376;
        cursor: pointer;
      }
    }
    .calendarBox {
      width: 100%;
      height: calc(100% - 48px);
      .riliB {
        width: 100%;
        // height: 204px;
        .selectdate {
          font-size: 24px;
          color: #fff;
          z-index: 99;
        }
        .showsdate {
          position: absolute;
          background: linear-gradient(180deg, rgba(22, 119, 255, 0.15), rgba(22, 119, 255, 0.8));
          // width: 74px;
          width: 88%;
          height: 80px;
          top: -38px;
          left: 8px;
        }
        .newBorder {
          border: 1px solid #e5e6eb;
          border-right: none;
          &:last-child {
            border-right: 1px solid #e5e6eb;
          }
        }
      }
      .richengList {
        width: 100%;
        height: 380px;
        overflow: auto;
        .richengLi {
          border-radius: 6px;
          width: 100%;
          // height: 110px;
          box-sizing: border-box;
          border-bottom: 1px solid #ecedf0;
          background: linear-gradient(90deg, #e6f4ff 0%, rgba(230, 244, 255, 0.65) 100%);
          margin: 6px 0;
          padding: 2px 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          &:last-child {
            border-bottom: none;
          }
          .richengLeft {
            width: calc(100% - 60px);
            height: 80px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .img {
              width: 50px;
              height: 50px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .wenzi {
              width: calc(100% - 80px);
              height: 80px;
              margin-left: 6px;
              .title {
                width: 100%;
                height: 40px;
                font-size: 16px;
                color: #000000;
                line-height: 40px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .decSmall {
                width: 100%;
                height: 40px;
                font-size: 14px;
                color: #717376;
                line-height: 40px;
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .spanBox {
                  width: 48px;
                  min-width: 48px;
                  height: 24px;
                  line-height: 16px;
                  margin-right: 10px;
                  display: inline-block;
                  padding: 2px 6px;
                  font-size: 16px;
                  color: #1677ff;
                  border: 1px solid #1677ff;
                  // background: #f5f6ff;
                  border-radius: 2px;
                }
                .spanBoxG {
                  width: 48px;
                  min-width: 48px;
                  height: 24px;
                  line-height: 16px;
                  margin-right: 10px;
                  display: inline-block;
                  padding:2px 6px;
                  font-size: 16px;
                  color: #00d9a9;
                  border: 1px solid #00d9a9;
                  // background: #e3f9e9;
                  border-radius: 2px;
                }
                span {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
          .richengRight {
            min-width: 40px;
            color: #1677ff;
            font-size: 16px;
            cursor: pointer;
          }
        }
      }
      .addRicheng {
        width: 100%;
        height: 56px;
        cursor: pointer;
        text-align: center;
        line-height: 56px;
        color: #000;
        font-size: 16px;
        background: #f5f7fa;
        border-radius: 8px;
        margin-top: 10px;
      }
    }
  }
</style>
