// 资源库

import { defHttp } from '/@/utils/http/axios';
import {
  IResourceLibraryParams
} from './model/resourceLibraryModel';

enum Api {
  SYS_RESOURCE_LIBRARY = '/system/sysUserResourceLibrary/all',
  SYS_RESOURCE_LIBRARY_SAVE = '/system/sysUserResourceLibrary/save',
}

/**
 * 获取用户资源库
 * @param params
 */
export const getSysUserResourceLibrary = (params: IResourceLibraryParams) => defHttp.get<any>({
  url: Api.SYS_RESOURCE_LIBRARY,
  params
});

/**
 * 用户资源库保存
 * @param params
 */
export const postSysUserResourceLibrarySave = (params: IResourceLibraryParams) => defHttp.post<any>({
  url: Api.SYS_RESOURCE_LIBRARY_SAVE,
  params,
  headers: {
    'Content-Type':'application/x-www-form-urlencoded'
  }
});
