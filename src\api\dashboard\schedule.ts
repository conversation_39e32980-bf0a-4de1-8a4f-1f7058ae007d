/**
 * @Name schedule
 * @Description：日程安排
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 17:17
 * @FilePath: src\api\dashboard\schedule.ts
 */

import { defHttp } from '/@/utils/http/axios';
import {
  ScheduleListGetResultModel,
  ScheduleParams, WeekDaysGetResultModel
} from './model/scheduleModel';

enum Api {
  SYS_SCHEDULE_LIST = '/sys/sysSchedule/list',
  SYS_SCHEDULE_WEEKDAYS = '/sys/sysSchedule/weekDays',
  SYS_SCHEDULE_WEEK_COUNT = '/sys/sysSchedule/weekCount',
  SYS_SCHEDULE_STATISTICS_COUNT = '/sys/sysSchedule/statistics/count',
}

// Get personal center-basic settings
export const getSysScheduleList = (params: ScheduleParams) => defHttp.get<ScheduleListGetResultModel>({
  url: Api.SYS_SCHEDULE_LIST,
  params
});

type weekDaysParams = {
  month: string;
  year: string;
  week: number;
}
/**
 * 查询周日期数据
 * @param params
 */
export const getSysScheduleWeekDays = (params: weekDaysParams) => defHttp.get<WeekDaysGetResultModel>({
  url: Api.SYS_SCHEDULE_WEEKDAYS,
  params
});
/**
 * 查询统计列表
 */
export const getWeekCountApi = () => defHttp.get({
  url: Api.SYS_SCHEDULE_WEEK_COUNT,
});
//
// export const getSysScheduleList = (params: ScheduleParams) => defHttp.get<ScheduleListGetResultModel>({
//   url: Api.SYS_SCHEDULE_STATISTICS_COUNT,
//   params
// });
