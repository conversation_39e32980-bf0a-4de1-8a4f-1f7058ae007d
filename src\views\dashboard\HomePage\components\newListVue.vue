<template>
  <div class="scheduleBox">
    <div class="smallTit">
      <div class="divList">
        <span :class="navNewIndex == index ? 'active' : ''" v-for="(item, index) in navNewlistTit" :key="index" @click="clickTitle(index)">
          {{ item.title }}
        </span>
      </div>
      <!-- <div class="more" @click="handleMore">更多></div> -->
    </div>
    <div class="noticeList" v-if="navNewIndex == 0">
      <div class="noticeLi" v-for="(item, index) in noticeList.list" :key="index" @click="newInfo(item.link)">
        <div class="leftImgTit">
          <div class="leftImg">
            <img :src="item.picturePath ? item.picturePath : zixun" alt="" />
          </div>
          <div class="leftTitleBox">
            <div class="tit">{{ item.title }}</div>
            <div class="decT">
              <span>{{ item.sendTime.substring(0, 10) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="noticeList" v-if="navNewIndex == 1">
      <div class="noticeLi" v-for="(item, index) in noticeList.list1" :key="index" @click="newInfo(item.link)">
        <div class="leftImgTit">
          <!-- <div class="leftImg">
            <img :src="zixun" alt="" />
          </div> -->
          <div class="leftTitleBox1">
            <div class="tit">{{ item.title }}</div>
            <div class="decT">
              <span>{{ item.sendTime.substring(0, 10) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import zixun from '/@/assets/images/homeImg/zixun.png';
  import { ref, reactive, onMounted } from 'vue';
  import { getSysNewsList } from '../index.api';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const navNewlistTit = ref([
    {
      title: '企业新闻',
      id: 1,
    },
    {
      title: '行业资讯',
      id: 2,
    },
  ]);
  const navNewIndex = ref(0);
  function clickTitle(val) {
    navNewIndex.value = val;
  }
  const noticeList = reactive({
    list: [
      //   {
      //     title: '魏桥创业管理学院2024年24年24开',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-06-16',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警2',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-06-06',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警3',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-05-26',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警4',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-05-16',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警5',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-05-06',
      //   },
    ],
    list1: [
      //   {
      //     title: 'new魏桥创业管理学院2024年24年24开',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-06-16',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警2',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-06-06',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警3',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-05-26',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警4',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-05-16',
      //   },
      //   {
      //     title: '周升温过快-周升温过快告警5',
      //     caohao: '1304',
      //     jieguo: '下发成功',
      //     shijianzhi: '4490',
      //     time: '2024-05-06',
      //   },
    ],
  });
  function getNewList() {
    getSysNewsList({ type: '1', page: '1', pageSize: '5' }).then((res) => {
      noticeList.list = res.records;
    });
  }
  function getZxList() {
    getSysNewsList({ type: '2', page: '1', pageSize: '5' }).then((res) => {
      noticeList.list1 = res.records;
    });
  }
  //   新闻详情
  function newInfo(item) {
    // router.push(item);
    // window.location.href = item;
    window.open(item);
  }
  onMounted(() => {
    getNewList();
    getZxList();
  });
  //   点击新闻跳转更多
  //   function handleMore() {
  //     window.open('//www.weiqiaocy.com/cn/news.html');
  //     // window.location.href = '//www.weiqiaocy.com/cn/news.html';
  //     // router.push('//www.weiqiaocy.com/cn/news.html');
  //   }
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 688px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    .smallTit {
      font-size: 22px;
      height: 60px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ecedf0;
      .divList {
        font-size: 22px;
        span {
          display: inline-block;
          height: 58px;
          line-height: 58px;
          color: #000;
          border-bottom: 2px solid transparent;
          margin-right: 20px;
          cursor: pointer;
        }
        .active {
          font-weight: bold;
          color: #1677ff;
          border-bottom: 2px solid #1677ff;
        }
      }
      .more {
        font-size: 20px;
        color: #717376;
        cursor: pointer;
      }
    }
    .noticeList {
      width: 100%;
      height: calc(100% - 80px);
      .noticeLi {
        width: 100%;
        height: 113px;
        border-bottom: 1px solid #ecedf0;
        display: flex;
        justify-content: center;
        align-items: center;
        &:last-child {
          border-bottom: none;
        }
        .leftImgTit {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          cursor: pointer;
          .leftImg {
            width: 152px;
            height: 86px;
            margin-right: 16px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .leftTitleBox {
            width: calc(100% - 168px);
            .tit {
              width: 100%;
              font-size: 18px;
              line-height: 46px;
              color: #24272d;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
            .decT {
              width: 100%;
              font-size: 16px;
              color: #717376;
              line-height: 40px;
              span {
                color: #717376;
                margin-right: 5px;
              }
            }
          }
          .leftTitleBox1 {
            width: 100%;
            .tit {
              width: 100%;
              font-size: 16px;
              line-height: 46px;
              color: #24272d;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
            .decT {
              width: 100%;
              font-size: 16px;
              color: #717376;
              line-height: 40px;
              span {
                color: #717376;
                margin-right: 5px;
              }
            }
          }
        }
      }
    }
  }
</style>
