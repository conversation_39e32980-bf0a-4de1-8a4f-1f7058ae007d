<template>
  <div :style="menuWidth" style="float: left; padding-left: 10px" class="file-menu">
    <div class="uploadFile">
      <div class="upload-icon">
        <a-button
          v-if="props.typeData !== 'star' && (createBy === 'super_admin' || createBy === 'admin')"
          type="primary"
          preIcon="ant-design:upload-outlined"
          @click="onUpload"
        >
          导入</a-button
        >
      </div></div
    >
    <a-table
      :loading="loading"
      :pagination="iPagination"
      rowKey="id"
      :columns="tableColumns"
      :dataSource="dataSource"
      :customRow="customRow"
      @change="handleTableChange"
    >
      <template v-slot:bodyCell="{ column, record, text }">
        <!-- {{ column }} -->
        <template v-if="column.key === 'fileName'">
          <div class="editable-cell">
            <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
              <a-input
                :ref="(el) => setItemRef(el, `rename${record.id}`)"
                style="width: 90%"
                v-model:value="editableData[record.id].fileName"
                @blur="inputSave(record.id)"
                @pressEnter="inputSave(record.id)"
              />
            </div>
            <div v-else class="editable-cell-text-wrapper">
              <div style="display: flex">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap" v-if="record.fileType !== 'folder'" class="pointer">
                  <img class="file-image" v-if="record.fileType === 'image'" :src="getImageSrc(record.url)" @click="handleTextClick(record)" />

                  <Icon
                    v-else-if="record.fileType === 'excel'"
                    class="file-icon"
                    icon="ant-design:file-excel-outlined"
                    style="color: rgb(98, 187, 55)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'pdf'"
                    class="file-icon"
                    icon="ant-design:file-pdf-outlined"
                    style="color: rgb(211, 47, 47)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'doc'"
                    class="file-icon"
                    icon="ant-design:file-word-outlined"
                    style="color: rgb(68, 138, 255)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'pp'"
                    class="file-icon"
                    icon="ant-design:file-ppt-outlined"
                    style="color: rgb(245, 124, 0)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'video'"
                    class="file-icon"
                    icon="ant-design:play-square-outlined"
                    style="color: rgb(119, 87, 188)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'text'"
                    class="file-icon"
                    icon="ant-design:file-text-outlined"
                    style="color: rgb(41, 211, 178)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'zip'"
                    class="file-icon"
                    icon="ant-design:file-zip-outlined"
                    style="color: rgb(253, 202, 7)"
                    @click="handleTextClick(record)"
                  />
                  <img :src="LinkImg" v-else-if="(record.ext = 'url')" class="file-image" @click="handleTextClick(record)" />
                  <Icon v-else class="file-icon" icon="ant-design:file-unknown-outlined" @click="handleTextClick(record)" />

                  <span
                    v-if="record.fileType === 'image' || record.ext === 'url'"
                    style="margin-left: 8px"
                    class="file-text"
                    @click="handleTextClick(record)"
                  >
                    {{ text + (record.ext ? '.' + record.ext : '') }}
                  </span>
                  <span v-else style="margin-left: 30px" class="file-text" @click="handleTextClick(record)">
                    {{ text + (record.ext ? '.' + record.ext : '') }}
                  </span>
                </div>
                <span style="">
                  <Icon v-if="record.izStar === '1'" icon="ant-design:star-outlined" style="color: #ff9800; margin-left: 4px; margin-top: 10px"
                /></span>
              </div>
              <div class="editable-cell-icon" v-if="selectedRowKeys.length < 2">
                <Icon style="top: -6px" icon="ant-design:star-outlined" class="header-icon-color pointer" @click="starUpdate(record)" />
                <Icon
                  style="top: -6px; left: 10px"
                  class="header-icon-color pointer"
                  icon="ant-design:cloud-download-outlined"
                  @click="downFile(record)"
                />
                <a-dropdown :trigger="['click']">
                  <Icon
                    class="header-icon-color pointer"
                    v-if="createBy === 'super_admin' || createBy === 'admin'"
                    style="top: -6px; left: 20px"
                    icon="ant-design:ellipsis-outlined"
                    @click.prevent
                  />
                  <template #overlay>
                    <a-menu class="menu-more" v-if="delFlag === '1'">
                      <span
                        style="text-align: center; display: block"
                        v-if="authority === 'readonly' || (authority === 'editable' && record.createBy !== createBy)"
                        >暂无权限</span
                      >
                      <a-menu-item
                        key="0"
                        v-if="(authority !== 'readonly' && authority !== 'editable') || (authority === 'editable' && record.createBy === createBy)"
                      >
                        <div @click="reductionFile(record)">
                          <Icon class="table-icon-color pointer" icon="ant-design:rollback-outlined" />
                          <span class="table-font-size">还原</span>
                        </div>
                      </a-menu-item>
                      <a-menu-item
                        key="0"
                        v-if="(authority !== 'readonly' && authority !== 'editable') || (authority === 'editable' && record.createBy === createBy)"
                      >
                        <div @click="removeCompletelyFile(record.id)">
                          <Icon class="table-icon-color pointer" icon="ant-design:delete-filled" />
                          <span class="table-font-size">彻底删除</span>
                        </div>
                      </a-menu-item>
                    </a-menu>
                    <a-menu class="menu-more" v-else>
                      <a-menu-item
                        key="0"
                        v-if="
                          record.ext === 'url' &&
                          ((authority === 'editable' && record.enableUpdat === '1') || authority === 'owner' || authority === 'admin')
                        "
                      >
                        <div @click="editLink(record)">
                          <Icon class="table-icon-color pointer" icon="ant-design:edit-outlined" />
                          <span class="table-font-size">编辑</span>
                        </div>
                      </a-menu-item>

                      <a-menu-divider v-if="record.izFolder === '0'" />
                      <a-menu-item key="1">
                        <div @click="inputEdit(record.id)">
                          <Icon class="table-icon-color pointer" icon="ant-design:edit-outlined" />
                          <span class="table-font-size">重命名</span>
                        </div>
                      </a-menu-item>
                      <a-menu-item key="6" v-if="authority !== 'readonly' && (authority !== 'editable' || record.createBy === createBy)">
                        <div @click="deleteFile(record.id)">
                          <Icon class="table-icon-color pointer" icon="ant-design:delete-outlined" />
                          <span class="table-font-size">删除</span>
                        </div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <uploadFileModal @register="registerModalJimport" :url="getFilesUploadSysFileUrl" :uploadSetting="state.uploadSettings" @ok="getFileList" />
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { ref, unref, onMounted, reactive, nextTick, computed, watch } from 'vue';
import { columns, miniColumns } from '../index.data';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';
import { useModal } from '@/components/Modal';
import uploadFileModal from './uploadFileModal.vue';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
import { filesDelete, getFilesListApi, addFavo, listFavo, deleteFavo, getFilesUploadSysFileUrl, saveOrUpdateFiles, deleteFiles } from '../index.api';
import { useContextMenu } from '/@/hooks/web/useContextMenu';
import LinkImg from '/@/assets/images/link.png';
import { useGlobSetting } from '@/hooks/setting';
import { encryptByBase64 } from '@/utils/cipher';
const loading = ref<boolean>(false);
const glob = useGlobSetting();
const tableColumns = ref<any>(columns);
const dataSource = ref<any>([]);
const $message = useMessage();
const selectedRowKeys = ref<any>([]);
const [registerModalJimport, { openModal: openModalJimport }] = useModal();
const editableData = reactive({});
const activeItem = ref(null);
const rowData = ref<any>({});
// const menuWidth = ref<any>({ width: 'calc(100% - 260px)' });
tableColumns.value = columns;
const userStore = useUserStore();
const createBy = userStore.getUserInfo.username;
const authority = ref<string>('');
const serchContent = ref('');
const state = reactive({
  data: [],
  headerSearchValue: '',
  headerSearchState: false,
  loading: false,
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10,
  },
  getFilesListApiParma: {
    // izFolder: null,
    izRootFolder: 1,
    parentId: '',
  },
  breadCrumbs: [
    {
      id: 0,
      name: '根目录',
    },
  ],
  uploadSettings: {
    params: {
      bizPath: '',
      parentId: '',
    },
    // updataAccept: '*',
    updataAccept: '.xls,.xlsx,.doc,.docx,.ppt,.pptx,.gif,.jpg,.jpeg,.png,.txt,.avi,.mov,.rmvb,.rm,.flv,.mp4,.zip,.pdf',
    filesParamsName: 'files',
  },
});
function inputEdit(key) {
  editableData[key] = cloneDeep(dataSource.value.filter((item) => key === item.id)[0]);
}
let inputRefs: any = {};
const pageSetting = {
  total: 0,
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
};
const iPagination = ref<any>(pageSetting);

const props = defineProps({
  typeData: String,
  keyword: String,
});

onMounted(async () => {
  await getFileList();
});
//重命名
async function inputSave(key) {
  let params = {};
  params = { fileName: editableData[key].fileName, id: editableData[key].id };
  await saveOrUpdateFiles(params, true);
  delete editableData[key];
  await getFileList();
}
/**
 * 删除文件
 */
async function deleteFile(id) {
  await filesDelete({ id: id });
  // 在删除操作完成后检查当前页数据
  if (dataSource.value.length === 1 && iPagination.value.current > 1) {
    // 如果当前页只有一条数据（即删除的这一条），并且不在第一页，那么翻页到上一页
    iPagination.value.current -= 1;
  }
  await getFileList();
}
const rowSelection = computed(() => {
  return {
    selectedRowKeys: unref(selectedRowKeys),
    onChange: onSelectChange,
  };
});
const onSelectChange = (changableRowKeys) => {
  selectedRowKeys.value = changableRowKeys;
};
/**
 * 获取文件内容
 */
function getFileList() {
  const apiCall = props.typeData === 'star' ? listFavo : getFilesListApi;
  loading.value = true;
  let params = {
    parentId: '2',
    fileName: `*${serchContent.value}*`,
    pageNo: iPagination.value.current,
    pageSize: iPagination.value.pageSize,
    column: 'createTime',
    order: 'desc',
  };
  dataSource.value = [];
  apiCall(params)
    .then((res) => {
      if (res.records && res.records.length > 0) {
        let records = res.records;
        dataSource.value = records;
        iPagination.value.total = res.total; // 更新总记录数
        //如果是分享那么需要更新标题
      } else {
        dataSource.value = [];
        iPagination.value.total = 0;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
/**
 * 渲染表格事件
 */
const customRow = (record) => {
  return {
    // onClick: (event) => {
    //   onRowClick(event, record);
    // },
    onDblclick: (event) => {
      onRowDblclick(event, record);
    },
  };
};
// async function onRowClick(event, record) {
//   await getFileList();
// }
const [createContextMenu] = useContextMenu();
/**
 * 文件下载
 * @param record
 */
// function downFile(record) {
//   updateDownNum({ fileId: record.id, userId: userStore.getUserInfo.id, operateType: 'used' });
//   //文件下载
//   if (record.izFolder == '0' && record.ext !== 'url') {
//     downloadFile(record.url);
//   } else {
//     downFileById(record.id, record.fileName);
//   }
// }
const downFile = async (activeItem) => {
  if (!unref(activeItem)?.id || unref(activeItem)?.fileType === 'folder') {
    message.warning('请选择文件');
    return;
  }

  const url = unref(activeItem)?.url;
  const fileName = `${activeItem.fileName}${activeItem.ext ? `.${activeItem.ext}` : ''}`;

  if (!url) {
    message.error('文件URL不存在');
    return;
  }

  try {
    // 通过 fetch 获取文件数据
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('文件获取失败');
    }

    const blob = await response.blob();
    const downloadUrl = URL.createObjectURL(blob);

    // 创建一个链接元素
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName; // 指定下载的文件名
    link.style.display = 'none';

    // 触发点击事件
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放 URL 对象
    URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('下载文件失败', error);
    message.error('下载文件失败，请稍后再试');
  }
};
/**
 * 文本点击事件
 */
// function handleTextClick(record) {
//   if (record.izFolder === '1') {
//     parentId.value = record.id;
//     let filter = titleData.value.filter((item) => item.key == record.id);
//     if (!filter || filter.length == 0) {
//       titleData.value.push({ key: record.id, title: record.fileName });
//     }
//     emit('setTitleData', { titleData: titleData.value, parentId: unref(parentId), pid: record.parentId });
//     type.value = '';
//     keyword.value = '';
//     //update-begin---author:wangshuai ---date:20221102  for：[VUEN-2608]删除文件夹至回收站，如果文件夹没有删除，列表不显示------------
//     if (unref(delFlag) === '0') {
//       unref(iPagination).current = 1;
//       getFileList();
//     } else {
//       //获取回收站
//       getRecycleFileList();
//     }
//     //update-end---author:wangshuai ---date:20221102  for：[VUEN-2608]删除文件夹至回收站，如果文件夹没有删除，列表不显示--------------
//     //打开文件夹隐藏属性
//     if (record.izFolder == '1') {
//       if (unref(expand)) {
//         fileAttributesRef.value.clear(true);
//       }
//     }
//     //文件名称点击之后更新搜索文件名称
//     emit('update-input', record.fileName);
//   } else {
//     //预览文件接口
//     handleView(record);
//   }
// }
const handleTextClick = (item) => {
  let currentItem = item || unref(activeItem);
  if (!currentItem?.id || currentItem?.fileType === 'folder' || currentItem.editing || currentItem.editable) {
    createMessage.warning('请选择文件');
    return null;
  }
  let url = `${'//ipms.corp.hongqiaocloud.com/kkfile/onlinePreview'}?url=` + encodeURIComponent(encryptByBase64(currentItem?.url));
  window.open(url, '_blank');
};
/**
 * 双击事件打开
 * @param event
 * @param record
 */
function onRowDblclick(event, record) {
  handleTextClick(record);
}
function setItemRef(el, key) {
  if (el) {
    nextTick(() => {
      inputRefs[key] = el;
      inputRefs[key].focus();
    });
  }
}
/**
 * 表格改变事件
 */
function handleTableChange(pagination) {
  iPagination.value = pagination;
  getFileList();
}
/**
 * 右侧菜单事件
 * @param event
 * @param record
 */
//  function onRowContextmenu(event, record) {
//   rowData.value = record;
//   event.preventDefault();

//   //update-begin---author:wangshuai ---date:20221011  for：[VUEN-2430]只读/编辑权限 对文件和文件夹的操作控制------------
//   //右侧菜单拼写
//    //右侧菜单拼写
//    let item: any = [
//     {
//       label: '打开',
//       icon: 'bx:bxs-folder-open',
//       handler: () => {
//         onRowDblclick(event, record);
//       },
//     },
//     {
//       label: '下载',
//       icon: 'ant-design:cloud-download-outlined',
//       divider: true,
//       handler: () => {
//         downFile(record);
//       },
//     },
//     {
//       label: '分享',
//       icon: 'ant-design:share-alt-outlined',
//       handler: () => {
//         onShareFile(record);
//       },
//       divider: true,
//     },
//     {
//       label: '重命名',
//       icon: 'ant-design:edit-outlined',
//       handler: () => {
//         inputEdit(record.id);
//       },
//     },
//     {
//       label: '移动到...',
//       icon: 'ant-design:swap-outlined',
//       handler: () => {
//         moveFile(record.id);
//       },
//     },
//     {
//       label: '复制到...',
//       icon: 'ant-design:copy-outlined',
//       handler: () => {
//         copyFile(record.id);
//       },
//     },
//     {
//       label: '删除',
//       icon: 'ant-design:delete-outlined',
//       handler: () => {
//         deleteFiles(record.id);
//       },
//     },
//     {
//       label: '属性',
//       icon: 'ant-design:info-circle-filled',
//       handler: () => {
//         rightMenuAtt.value = true;
//         setAttribute(record);
//       },
//     },
//   ];

//   //如果不是文件夹，需要添加预览/标星/上传新版本
//   if (record.izFolder == '0') {

//     item.splice(2, 0, {
//       label: record.izStar == '0' ? '标星' : '取消标星',
//       icon: 'ant-design:star-outlined',
//       handler: () => {
//         starUpdate(record);
//       },
//     });
//   }

//   //如果后缀是url，那么有编辑
//   if (record.ext == 'url') {
//     //如果是只读或者编辑权限允许修改
//     if (unref(authority) !== 'readonly' || (unref(authority) === 'editable' && record.enableUpdat == '1')) {
//       item.splice(1, 0, {
//         label: '编辑',
//         icon: 'ant-design:edit-outlined',
//         handler: () => {
//           editLink(record);
//         },
//       });
//     }
//     //删除上传新版本
//     item.splice(7, 1);
//   }

//   //如果只有编辑权限，那么就没有删除操作，编辑权限不为创建者才会修改创建者
//   if (unref(authority) == 'readonly' || (unref(authority) == 'editable' && record.createBy !== createBy)) {
//     if (unref(authority) == 'editable') {
//       item.splice(item.length - 2, 1);
//     }
//     //如果权限只读并且不是文件夹的情况下，那么只留预览、下载、标星、复制到和属性
//     if (unref(authority) == 'readonly' && record.izFolder == '0') {
//       item.splice(4, 2);
//       item.splice(4, 3);
//     }
//     //如果权限只读并且是文件夹的情况下，那么只留预览、下载、复制到和属性
//     if (unref(authority) == 'readonly' && record.izFolder == '1') {
//       item.splice(3, 2);
//       item.splice(3, 2);
//     }
//     //如果不予许下载，移除下载选项,并且不为当前创建者
//     if (record.enableDown == '0' && record.createBy !== createBy) {
//       item.splice(1, 1);
//     }
//     //如果不予许编辑，移除上传新版本和复制到...
//     if (record.enableUpdat == '0') {
//       //上传新版本的坐标
//       let uploadIndex = -1;
//       //移动到的坐标
//       let moveToIndex = -1;
//       //重命名
//       let renameIndex = -1;
//       for (let i = 0; i < item.length; i++) {
//         if (item[i].label === '移动到...') {
//           moveToIndex = i;
//         }
//         if (item[i].label === '上传新版本' && record.ext !== 'url') {
//           uploadIndex = i;
//         }
//         if (item[i].label === '重命名') {
//           renameIndex = i;
//         }
//       }
//       //移除上传至
//       if (uploadIndex != -1) {
//         item.splice(uploadIndex, 1);
//       }
//       //移除复制到...
//       if (moveToIndex != -1) {
//         item.splice(moveToIndex, 1);
//       }
//       //移除重命名
//       if (renameIndex != -1) {
//         item.splice(renameIndex, 1);
//       }
//     }
//   }
//   createContextMenu({
//     event: event,
//     styles: { 'z-index': 512, 'font-size': '13px' },
//     items: item,
//   });
//   //update-end---author:wangshuai ---date:20221011  for：[VUEN-2430]只读/编辑权限 对文件和文件夹的操作控制--------------
// }

/**
 * 获取图片路径
 * @param url
 */
function getImageSrc(url) {
  return getFileAccessHttpUrl(url);
}
/**
 * 标星更新
 * @param record
 */
async function starUpdate(record) {
  //update-begin---author:wangshuai ---date:20221011  for：传递record参数，抽出通用方法--------------
  let izStar = record.izStar;
  let id = record.id;
  let params = { id: id };
  //update-end---author:wangshuai ---date:20221011  for：传递record参数，抽出通用方法--------------
  if (izStar == '1') {
    await deleteFavo({ id: id });
    getFileList();
  } else {
    await addFavo({ id: id });
    getFileList();
  }
}
const onUpload = () => {
  let length = state.breadCrumbs.length;
  let lastChild = state.breadCrumbs[length - 1];
  state.uploadSettings = {
    ...state.uploadSettings,
    params: {
      bizPath: `sys/manage/2`,
      parentId: '2',
    },
  };
  openModalJimport(true);
};
watch(
  () => props.typeData,
  () => {
    // props.keyword=''
    serchContent.value = '';
    iPagination.value = { ...pageSetting };
    getFileList();
  }
);
watch(
  () => props.keyword,
  () => {
    serchContent.value = props.keyword || '';
    getFileList();
  }
);
// 监视iPagination变化
// watch(iPagination, () => {
//   getFileList();
// }, {
//   deep: true
// });
</script>

<style scoped lang="less">
:deep(.ant-table-tbody > tr > td) {
  padding: 0 16px;
  height: 36px;
  line-height: 36px;
}
.uploadFile {
  overflow: hidden;
  background: white;
  height: 50px;
}
.upload-icon {
  margin: 10px 0px 0px 10px;
}

.add {
  color: white;
  background-color: #1e88e5;
  border-radius: 20px;
}

.upload-font-size {
  font-size: 13px;
}

.border-hide {
  border: none;
}

.editable-cell {
  position: relative;

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 90px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 30px;
    width: 40px;
    cursor: pointer;
    top: 10px;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: none !important;
    color: #999;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}

.editable-cell:hover .editable-cell-icon {
  display: inline-block !important;
}

.title-hover:hover {
  color: #1e88e5;
}

.file-icon {
  height: 28px;
  margin-top: 4px;
  position: absolute;
  vertical-align: top;
  width: 26px;
  left: 1px;
  :deep(svg) {
    width: 100% !important;
    height: 100% !important;
  }
}

.file-image {
  display: inline-block !important;
  height: 24px;
  margin-right: 2px;
  margin-top: 6px;
  vertical-align: top;
  width: 21px;
}
.pointer {
  cursor: pointer;
}

.file-text:hover {
  color: rgb(51, 51, 51);
  text-decoration: underline;
}
.file-menu {
  overflow: hidden;
  float: left;
  // margin-top: 10px;
}
:deep(.ant-table-selection-column) {
  padding: 0 !important;
}
.share {
  display: table;
  height: 150px;
  width: 100%;
}
.file-form {
  height: 200px;
  .file-item {
    margin-bottom: 24px;
    padding-left: 100px;
    position: relative;
  }

  .file-item-label {
    color: #9e9e9e;
    font-size: 14px;
    height: 36px;
    left: 0;
    line-height: 36px;
    position: absolute;
    text-align: right;
    width: 80px;
  }
  .file-item-content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    top: 6px;
  }
}
.closed-tip {
  color: #919191;
  font-size: 13px;
}
.share-input {
  width: 230px;
  padding: 0 12px;
  box-sizing: border-box;
  font-size: 14px;
  height: 34px;
}
.share-span {
  font-size: 13px;
  border-radius: 3px;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  height: 34px;
  line-height: 34px;
  margin-left: 12px;
  text-align: center;
  width: 68px;
  box-sizing: border-box;
  background-color: #1e88e5;
}
.header-icon {
  display: inline;
  margin-left: 22px;
}
.header-icon-color {
  color: #999;
  font-size: 20px !important;
  position: relative;
  top: 2px;
}
.header-icon-color:hover {
  color: #1e88e5 !important;
}
.table-icon-color {
  display: inline;
  font-size: 13px !important;
}
.table-font-size {
  font-size: 13px;
  margin-left: 10px;
}
.menu-more {
  z-index: 512;
  width: 156px;
  :deep(.ant-dropdown-menu-item) {
    width: 100%;
    height: 42px !important;
    margin: 0 !important;
    line-height: 42px !important;
  }
}
</style>
