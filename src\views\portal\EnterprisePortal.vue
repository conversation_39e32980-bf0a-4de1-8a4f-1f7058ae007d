<template>
  <div class="enterprise-portal">
    <div v-if="hasSysAppPermission" style="display: flex; justify-content: flex-end; padding-bottom: 5vh; width: 90%">
      <a-button
        type="primary"
        ghost
        @click="handleSystemManage"
        style="
          display: flex;
          align-items: center;
          background: #e4f1ff !important;
          border-radius: 2px 2px 2px 2px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        "
      >
        <img style="width: 16px" class="anticon" src="@/assets/entry/system.png" />
        系统管理
      </a-button>
    </div>
    <!-- 主体内容 -->
    <main class="portal-main" style="width: 100%">
      <a-row>
        <a-col :xs="24" :sm="12" :md="12" :lg="12">
          <div class="portal-container">
            <div class="brand-wrapper">
              <img src="@/assets/entry/<EMAIL>" alt="魏桥创业集团标识" class="brand-logo" />
            </div>
            <div class="illustration-wrapper">
              <img src="@/assets/entry/<EMAIL>" class="brand-logo" alt="插画" />
            </div>
          </div>
        </a-col>
        <a-col :push="{ xs: 24, lg: 2, xl: 2 }" :xs="24" :sm="12" :md="12" :lg="8">
          <div class="unit-selector">
            <div class="unit-selector-title">请选择您要访问的电解分公司</div>
            <div class="unit-selector-sub-title">点击前往</div>
            <div class="unit-card-container">
              <div
                v-for="unit in corpList"
                :key="unit.id"
                class="unit-card"
                :style="{ cursor: unit.permission ? 'pointer' : 'not-allowed' }"
                @click="handleUnitSelect(unit)"
              >
                <div class="unit-logo">
                  <img :src="getShowBg(unit)" />
                </div>
                <div class="unit-info">
                  <h3 class="unit-name">{{ unit.name }}</h3>
                  <p class="unit-description">智慧工厂</p>
                </div>
                <div v-show="!unit.permission" class="unit-last disabled"> 无权限 </div>
                <div v-show="unit.departmentId && unit.departmentId === curDepartment && unit.permission" class="unit-last"> 上次访问 </div>
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: center; padding-top: 20px">
            <a-button type="primary" ghost shape="round" @click="handleLogout"><a-icon type="swap-outlined"></a-icon> 切换账号</a-button>
          </div>
        </a-col>
      </a-row>
    </main>
  </div>
</template>

<script setup>
  import { getUserDeparts, selectDepartment } from '/@/views/system/depart/depart.api';
  import { useRouter } from 'vue-router';
  import { isPermission } from '/@/api/common/api';
  // 导入图片
  import ly1BgImgDefault from '@/assets/entry/<EMAIL>';
  import ly1BgImgActive from '@/assets/entry/<EMAIL>';
  import ly1BgImgDisabled from '@/assets/entry/<EMAIL>';
  import ly2BgImgDefault from '@/assets/entry/<EMAIL>';
  import ly2BgImgActive from '@/assets/entry/<EMAIL>';
  import ly2BgImgDisabled from '@/assets/entry/<EMAIL>';
  import ly3BgImgDefault from '@/assets/entry/<EMAIL>';
  import ly3BgImgActive from '@/assets/entry/<EMAIL>';
  import ly3BgImgDisabled from '@/assets/entry/<EMAIL>';
  import ly4BgImgActive from '@/assets/entry/<EMAIL>';
  import ly4BgImgDefault from '@/assets/entry/<EMAIL>';
  import ly4BgImgDisabled from '@/assets/entry/<EMAIL>';
  import { useUserStore } from '@/store/modules/user';
  import { computed, onMounted, ref } from 'vue';
  import { t } from '/@/hooks/web/useI18n';

  const router = useRouter();

  const userStore = useUserStore();

  // 生产单元数据
  const productionUnits = [
    {
      name: '默认',
      bgImg: ly4BgImgDefault,
      bgImgActive: ly4BgImgActive,
      bgImgDisabled: ly4BgImgDisabled,
    },
    {
      name: '铝业一公司',
      orgCode: 'A06',
      code: '104001',
      key: 'ly1',
      bgImg: ly1BgImgDefault,
      bgImgActive: ly1BgImgActive,
      bgImgDisabled: ly1BgImgDisabled,
    },
    {
      id: 'LY02',
      name: '铝业二公司',
      key: 'ly2',
      bgImg: ly2BgImgDefault,
      bgImgActive: ly2BgImgActive,
      bgImgDisabled: ly2BgImgDisabled,
      route: '/ly02/dashboard',
    },
    {
      id: 'LY03',
      name: '铝业三公司',
      key: 'ly3',
      code: '104003',
      bgImg: ly3BgImgDefault,
      bgImgActive: ly3BgImgActive,
      bgImgDisabled: ly3BgImgDisabled,
      route: '/ly03/dashboard',
    },
    {
      id: 'LY04',
      name: '铝业四公司',
      key: 'ly4',
      bgImg: ly4BgImgDefault,
      bgImgActive: ly4BgImgActive,
      bgImgDisabled: ly4BgImgDisabled,
      route: '/ly04/dashboard',
    },
    {
      name: '铝业五公司',
      key: 'ly4',
      orgCode: 'A07',
      code: '104007',
      bgImg: ly4BgImgDefault,
      bgImgActive: ly4BgImgActive,
      bgImgDisabled: ly4BgImgDisabled,
    },
  ];
  const corpList = ref([]);
  const curDepartment = ref(null);

  const getShowBg = computed(() => {
    return function (unit) {
      let bg = unit.bgImg;
      if (unit.departmentId && unit.departmentId === curDepartment.value) {
        bg = unit.bgImgActive;
      }
      if (!unit.permission) {
        bg = unit.bgImgDisabled;
      }
      return bg;
    };
  });

  const hasSysAppPermission = ref(false);
  const getHasAppPermission = async () => {
    let hasPermission = false;
    try {
      const res = await isPermission({ appCode: 'system' });
      hasPermission = res;
    } catch (error) {
      hasPermission = false;
    }
    hasSysAppPermission.value = hasPermission;
  };

  const handleLogout = () => {
    userStore.logout(true);
  };

  const handleSystemManage = () => {
    router.push({
      path: '/middle/system',
    });
  };

  onMounted(() => {
    // 初始化生产单元数据
    const corps = userStore.getUserCorp;
    getHasAppPermission();
    corpList.value = corps.map((corp) => {
      const unit = productionUnits.find((unit) => unit.code === corp.code);
      return {
        ...(unit ? unit : productionUnits[0]),
        ...corp,
      };
    });
    curDepartment.value = userStore.corpId;
  });

  const handleUnitSelect = async (unit) => {
    if (!unit.permission) return;

    try {
      // 切换部门
      const res = await selectDepartment({
        departmentId: unit.departmentId,
        appCode: 'portal',
      });
      // 选中的公司id保存到store中
      userStore.setCorpId(unit.departmentId);
      userStore.setTenant(unit.departmentId);
      curDepartment.value = unit.departmentId;

      router.push({ path: '/portal' });
    } catch (error) {
      console.log('切换部门失败', error);
    }
  };
</script>

<style lang="less" scoped>
  .enterprise-portal {
    min-height: 100vh;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .portal-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .brand-wrapper {
      margin-bottom: 40px;
    }

    .illustration-wrapper {
      //width: 80%;
      height: 60vh;
      text-align: center;

      img {
        height: 100%;
      }
    }
  }

  .unit-selector {
    max-width: 1200px;
    //width: 309px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .unit-selector-title {
      font-size: 18px;
      color: #333;
      margin-bottom: 20px;
      text-align: center;
    }

    .unit-selector-sub-title {
      height: 13px;
      font-size: 14px;
      color: #7d8091;
      line-height: 12px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-bottom: 20px;
    }

    .unit-card-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
      background-color: white;
      padding: 20px;
      height: 60vh;
      width: 80%;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.04);
      border-radius: 15px 15px 15px 15px;
      overflow-y: scroll;
      // scrollbar-width: none;
      // -ms-overflow-style: none;
      // &::-webkit-scrollbar {
      //   display: none;
      // }
    }

    .unit-card {
      //border: 1px solid #ebebeb;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;

      // &:hover {
      //   //box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      // }

      .unit-logo {
        width: 100%;
        min-width: 100%;
        object-fit: cover;
        //text-align: center;
        //margin-bottom: 10px;

        img {
          width: 100%;
        }
      }

      .unit-info {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // align-items: center;
      }

      .unit-name {
        font-size: 18px;
        color: #333;
        margin: 6px 0;
      }

      .unit-description {
        font-size: 14px;
        color: #666;
      }

      .unit-last {
        position: absolute;
        top: 0;
        right: 1%;
        padding: 0.7vh 0.5vw;
        background: #0973fb;
        border-radius: 0px 2px 0px 4px;
        font-size: 1.2vh;
        color: #ffffff;
        line-height: 11px;
        overflow: hidden;

        // 没权限
        &.disabled {
          background: #9ba4be;
        }
      }
    }
  }
</style>
