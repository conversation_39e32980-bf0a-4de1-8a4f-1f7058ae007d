<template>
  <WujieVue v-if="name" width="100%" height="100%" :name="name" :plugins="plugins" :props="{...props,name}" />
</template>
<script>

import { useRoute } from 'vue-router'
import { getProps } from '@/wujie/state';

const props = getProps()

const plugins = [
  {
    jsBeforeLoaders: [
      {
        callback: (appWindow) => {
          // appWindow.parent.document.exitFullscreen = appWindow.document.exitFullscreen
          Object.defineProperties(appWindow.document, {
            exitFullscreen: {
              get: () => {
                const res = appWindow.__WUJIE.degrade
                  ? appWindow.__WUJIE.document.exitFullscreen
                  : appWindow.parent.exitFullscreen;
                return res
              },
              set: (value) => {
                console.log(value)
                return value
              }
            }
          });
        },
      },
    ],
  },
]

export default {
  data() {
    return {
      name: '',
      plugins,
      props
    };
  },
  activated() {
    console.log('activated')
  },
  // beforeRouteEnter(to,from,next){
  //   this.name = to.path.split('middle/')[1]
  //   next(to)
  // },
  mounted() {
    const route = useRoute()
    this.name = route.path.split('middle/')[1]
    console.log('mounted', route, this.name);
  }
};
</script>
