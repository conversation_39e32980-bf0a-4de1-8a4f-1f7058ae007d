<template>
  <ConfigProvider :locale="getAntdLocale">
    <AppProvider>
        <RouterView />
        <DragBall v-if="permissionStore.showBall && showUrl" />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
  import { ConfigProvider } from 'ant-design-vue';
  import { AppProvider } from '/@/components/Application';
  import { useTitle } from '/@/hooks/web/useTitle';
  import { useLocale } from '/@/locales/useLocale';
  import DragBall from './components/DragBall/DragBall.vue';
  import { usePermissionStoreWithOut } from '/@/store/modules/permission';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';

  // 解决日期时间国际化问题
  import 'dayjs/locale/zh-cn';
  import { ref } from 'vue';
  
  // import moment from 'moment';
  // import 'moment/locale/zh-cn';
  // moment.locale('zh-cn');
  // support Multi-language
  const { getAntdLocale } = useLocale();
  
  useTitle();

  const permissionStore = usePermissionStoreWithOut();
  const showUrl = ref(window.location.pathname.includes('/portal'))

  listenerRouteChange((route) => {
    console.log('route:', route)
    showUrl.value = route.path === '/portal'
  });

</script>
<style lang="less">
  // update-begin--author:liaozhiyang---date:20230803---for：【QQYUN-5839】windi会影响到html2canvas绘制的图片样式
  img {
    display: inline-block;
  }
  // update-end--author:liaozhiyang---date:20230803---for：【QQYUN-5839】windi会影响到html2canvas绘制的图片样式
</style>
