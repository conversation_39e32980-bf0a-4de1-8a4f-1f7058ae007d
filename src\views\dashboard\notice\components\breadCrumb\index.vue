<template>
  <div class="notice-breadcrumb">
    <a-breadcrumb :routes="breadcrumbData" separator=">>">
      <template #itemRender="{ route, paths }">
        <span v-if="breadcrumbData.indexOf(route) === breadcrumbData.length - 1">
          {{ route.breadcrumbName }}
        </span>
        <router-link v-else :to="`/${paths.join('/')}`">
          {{ route.breadcrumbName }}
        </router-link>
      </template>
    </a-breadcrumb>
  </div>
</template>
<script lang="ts" name="system-notice" setup>
  import { ref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { PageEnum } from '@/enums/pageEnum';
  import { useUserStore } from '/@/store/modules/user';

  const userStore = useUserStore();

  const route = useRoute();
  const breadcrumbData = ref([]);

  onMounted(() => {
    getBreadcrumbData();
  });
  const getBreadcrumbData = () => {
    breadcrumbData.value = route.matched.map((item, index) => {
      return {
        path: index === 0 ? (userStore.getUserInfo.homePath || PageEnum.BASE_HOME) : item.path,
        breadcrumbName: item?.meta?.breadcrumbName,
      };
    });
  };
</script>
<style lang="less" scoped>
  .notice-breadcrumb {
    margin: 0 10px;
    padding: 10px;
    background: #fff;
  }
</style>
