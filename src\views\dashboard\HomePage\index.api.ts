import { defHttp } from '/@/utils/http/axios';

enum Api {
  userInfo = '/sys/user/info', // 用户信息
  noticeList = '/sys/annountCement/vue3List', // 通知公告
  sysNewsAll = '/sys/sysNews/list', // 企业新闻 ，行业资讯，行业洞察
  knowledgeList = '/sys/files/list', //知识库
  getUserPermissionByTokenGroupByApp = '/sys/permission/getUserPermissionByTokenGroupByApp', //获取所有有权限的菜单
  getFavorMenuList = '/sys/user/favor/getMenuList', //获取所有有权限的菜单
  saveOrUpdateFavorMenu = '/sys/user/favor/saveOrUpdate' , //收藏菜单
  //   all = '/sys/sysSchedule/all',
  //   save = '/sys/sysSchedule/add',
  //   workLogSave = '/sys/sysWorkLog/add',
  //   workLogAll = '/sys/sysWorkLog/all',
  //   statistics_count = '/sys/sysSchedule/statistics/count',
}

export const getUserPermissionByTokenGroupByApp = (params) => {
  return defHttp.get({ url: Api.getUserPermissionByTokenGroupByApp, params });
};

export const getFavorMenuList = (params = {}) => {
  return defHttp.get({ url: Api.getFavorMenuList, params });
};

export const saveOrUpdateFavorMenu = (params) => {
  return defHttp.post({ url: Api.saveOrUpdateFavorMenu, params });
};

export const getUserInfo = (params) => {
  return defHttp.get({ url: Api.userInfo, params });
};
export const getNoticeList = (params) => {
  return defHttp.get({ url: Api.noticeList, params });
};
export const getSysNewsList = (params) => {
  return defHttp.get({ url: Api.sysNewsAll, params });
};
export const getKnowledge = (params) => {
  return defHttp.get({ url: Api.knowledgeList, params });
};

// export const getworkLogListApi = (params) => {
//   return defHttp.get({ url: Api.workLogAll, params });
// };

// export const getStatisticsCountApi = (params) => {
//   return defHttp.get({ url: Api.statistics_count, params });
// };

/**
 * 保存日程或者日志
 * @param params
 * @param isUpdate
 */
// export const update = (params, isWorkLog) => {
//   let url = isWorkLog ? Api.workLogSave : Api.save;
//   return defHttp.post({ url: url, params });
// };
//

