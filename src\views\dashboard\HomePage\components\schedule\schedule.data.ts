import dayjs from 'dayjs';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
// import { render } from '/src/utils/common/renderUtils';

// export const columns: BasicColumn[] = [
//   {
//     title: '标题',
//     width: 150,
//     dataIndex: 'titile',
//   },
//   {
//     title: '消息类型',
//     dataIndex: 'msgCategory',
//     width: 100,
//     customRender: ({ text }) => {
//       return render.renderDict(text, 'msg_category');
//     },
//   },
//   {
//     title: '发布人',
//     width: 100,
//     dataIndex: 'sender',
//   },
//   {
//     title: '优先级',
//     dataIndex: 'priority',
//     width: 70,
//     customRender: ({ text }) => {
//       const color = text == 'L' ? 'blue' : text == 'M' ? 'yellow' : 'red';
//       return render.renderTag(render.renderDict(text, 'priority'), color);
//     },
//   },
//   {
//     title: '通告对象',
//     dataIndex: 'msgType',
//     width: 100,
//     customRender: ({ text }) => {
//       return render.renderDict(text, 'msg_type');
//     },
//   },
//   {
//     title: '发布状态',
//     dataIndex: 'sendStatus',
//     width: 70,
//     customRender: ({ text }) => {
//       const color = text == '0' ? 'red' : text == '1' ? 'green' : 'gray';
//       return render.renderTag(render.renderDict(text, 'send_status'), color);
//     },
//   },
//   {
//     title: '发布时间',
//     width: 100,
//     dataIndex: 'sendTime',
//   },
//   {
//     title: '撤销时间',
//     width: 100,
//     dataIndex: 'cancelTime',
//   },
// ];
export const detailNotice: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },

  {
    field: 'titile',
    label: '通知标题',
    component: 'Input',

    componentProps: {
      placeholder: '请输入标题',
    },
  },

  {
    field: 'sendTime',
    label: '发布时间',
    component: 'Input',
  },

  {
    field: 'senderName',
    label: '发布人',
    component: 'Input',
  },

  {
    field: 'msgContent',
    label: '通知内容',
    component: 'InputTextArea',
    colProps: { span: 24 },
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'title',
    label: '标题',
    component: 'JInput',
    colProps: { span: 8 },
  },
];

export const scheduleFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '日程名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入日程名称',
    },
  },
  {
    field: 'dateTime',
    label: '时间',
    component: 'RangePicker',
    required: true,
    defaultValue: [new Date(), new Date()],
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择截至日期',
    },

    dynamicRules: ({ model }) => rules.endTime(model.startTime, true),
  },
  {
    field: 'item',
    label: '日程内容',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      autoSize: { minRows: 9, maxRows: 50 },
      placeholder: '请输入日程内容(最多输入200个字符)',
      maxlength: 200,
    },
  },
];
export const logFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '标题',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入日志标题',
    },
  },
  {
    field: 'content',
    label: '日志内容',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      autoSize: { minRows: 9, maxRows: 50 },
      placeholder: '请输入日志内容(最多输入200个字符)',
      maxlength: 200,
    },
  },
];
export const tabsType = [
  {
    id: '0',
    tab: '当日日程',
    modalTab: '日程',
    key: 'schedule',
  },
  {
    id: '1',
    tab: '工作日志',
    modalTab: '工作日志',
    key: 'log',
  },
];
