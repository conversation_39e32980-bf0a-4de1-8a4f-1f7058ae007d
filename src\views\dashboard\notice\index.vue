<template>
  <div>
    <BreadCrumb />
    <BasicTable @register="registerTable" :rowSelection="null">
      <template #action="{ record }">
        <TableAction :actions="getActions(record)" />
      </template>
    </BasicTable>
    <DetailModal title="查看详情" @register="register" />
  </div>
</template>
<script lang="ts" name="system-notice" setup>
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import BreadCrumb from './components/breadCrumb/index.vue';
  import DetailModal from './DetailModal.vue';
  import { columns, searchFormSchema } from './notice.data';
  import { getMyAnnouncementSend } from './notice.api';
  import { useListPage } from '/@/hooks/system/useListPage';
  const [register, { openModal }] = useModal();

  // 列表页面公共参数、方法
  const { prefixCls, tableContext } = useListPage({
    designScope: 'notice-template',
    tableProps: {
      title: '消息通知',
      // api: getList,
      api: getMyAnnouncementSend,
      columns: columns,
      formConfig: {
        schemas: searchFormSchema,
      },
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;
  /**
   * 查看
   */
  function handleDetail(record) {
    openModal(true, {
      record,
      isUpdata: true,
    });
  }
  /**
   * 操作列定义
   * @param record
   */
  function getActions(record) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }
</script>
