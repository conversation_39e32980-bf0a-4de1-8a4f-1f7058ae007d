import WujieVue from 'wujie-vue3';

const { bus } = WujieVue;
export const wijieUtils = {
  watchIsMobile(value) {
    bus.$emit('watchIsMobile', value);
  },
  routeChange(value) {
    bus.$emit('routeChange', value);
  },
  loaded(callBack = () => {}) {
    bus.$on('loaded', (value) => {
      callBack && callBack(value);
    });
  },
  unloaded(callBack = () => {}) {
    bus.$on('unloaded', (value) => {
      callBack && callBack(value);
    });
  },
};
