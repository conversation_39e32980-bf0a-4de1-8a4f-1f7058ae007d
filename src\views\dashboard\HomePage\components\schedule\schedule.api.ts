import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/sys/sysSchedule/list',
  all = '/sys/sysSchedule/all',
  save = '/sys/sysSchedule/add',
  workLogSave = '/sys/sysWorkLog/add',
  workLogAll = '/sys/sysWorkLog/all',
  statistics_count = '/sys/sysSchedule/statistics/count',
}

export const getListApi = (params) => {
  return defHttp.get({ url: Api.all, params });
};
export const getworkLogListApi = (params) => {
  return defHttp.get({ url: Api.workLogAll, params });
};

export const getStatisticsCountApi = (params) => {
  return defHttp.get({ url: Api.statistics_count, params });
};

/**
 * 保存日程或者日志
 * @param params
 * @param isUpdate
 */
export const update = (params, isWorkLog) => {
  let url = isWorkLog ? Api.workLogSave : Api.save;
  return defHttp.post({ url: url, params });
};
