<template>
  <div class="scheduleBox">
    <div class="smallTit">
      <span style="font-weight: bold; font-size: 22px"> 消息中心 </span>
      <div class="more" @click="handleMore">更多 <right-outlined /></div>
    </div>
    <div class="noticeList">
      <div class="noticeLi" v-for="(item, index) in noticeList.list" :key="index" @click="noticeBtn(item)">
        <div class="leftImgTit">
          <div class="leftImg">
            <img :src="notice" alt="" />
          </div>
          <div class="leftTitleBox">
            <div class="tit" v-if="item.titile"> {{ item.titile }}</div>
            <!-- <div class="decT" v-if="item.caohao">
              <span>槽号：{{ item.caohao }}，</span>
              <span>时间结果：{{ item.jieguo }}，</span>
              <span>事件值：{{ item.shijianzhi }}</span>
            </div> -->
            <div class="decT" v-else v-html="item.msgContent"> </div>
          </div>
        </div>
        <div class="rightTime">
          <p>{{ item.sendTime.slice(0, 10) }} </p>
        </div>
      </div>
    </div>
  </div>
  <DetailModal title="查看详情" @register="register" />
</template>
<script setup lang="ts">
  import { RightOutlined } from '@ant-design/icons-vue';
  import { useModal } from '/@/components/Modal';
  import notice from '/@/assets/images/homeImg/notice.png';
  import DetailModal from '../../notice/DetailModal.vue';
  import { getNoticeList } from '../index.api';
  import { ref, reactive, onMounted, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { queryById, getMyAnnouncementSend } from '../../notice/notice.api';
  const router = useRouter();
  const [register, { openModal }] = useModal();
  const noticeList = reactive({
    list: [],
  });
  function handleMore() {
    router.push('/notice');
  }
  async function noticeBtn(item) {
    // try {
    //   let res = await queryById({ id: item.id });
    //   openDetail(res);
    // } catch (error) {
    openDetail(item);
    // }
  }
  function openDetail(record) {
    openModal(true, {
      record,
      isUpdata: true,
    });
  }
  //   获取消息中心
  function getNotice() {
    getMyAnnouncementSend({ pageNo: 1, pageSize: 5 }).then((res) => {
      noticeList.list = res.records;
    });
  }
  onMounted(() => {
    getNotice();
  });
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 100%;
    // height: 448px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
      .more {
        float: right;
        font-size: 16px;
        color: #717376;
        cursor: pointer;
      }
    }
    .noticeList {
      width: 100%;
      // height: calc(100% - 48px);
      height: 100%;
      .noticeLi {
        width: 100%;
        margin: 6px 0;
        padding: 7px;
        // height: 120px;
        border-bottom: 1px solid #ecedf0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:last-child {
          border-bottom: none;
        }
        .leftImgTit {
          width: calc(100% - 94px);
          display: flex;
          justify-content: flex-start;
          align-items: center;
          cursor: pointer;
          .leftImg {
            width: 46px;
            height: 47px;
            // margin-right: 16px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .leftTitleBox {
            margin-left: 12px;
            width: calc(100% - 60px);
            // line-height: 24px;
            .tit {
              width: 100%;
              min-height: 72px;
              font-size: 18px;
              padding: 12px 0;
              margin-top: 10px;
              color: #24272d;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .decT {
              width: 100%;
              padding: 12px 0;
              font-size: 12px;
              color: #717376;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              span {
                color: #717376;
                margin-right: 5px;
              }
            }
          }
        }
        .rightTime {
          // width: 84px;
          padding-top: 12px;
          font-size: 16px;
          color: #717376;
        }
      }
    }
  }
</style>
