<template>
  <div class="p-4 bodyq">
    <div style="width: 260px; margin-right: 10px">
      <left-file
        ref="leftFileRef"
        style="margin-right: 10px; width: 260px"
        @click-file-type="clickFileType"
        @recycle-upload-list="recycleUploadList"
        @search-list="searchList"
        @update-type="updateType"
      ></left-file>
    </div>
    <div>
      <right-file ref="rightFileRef" :typeData="typeData" :keyword="keyword" @setTitleData="handleTitleData" @update-input="updateInput"></right-file>
    </div>
  </div>
</template>

<script setup lang="ts">
import { unref, ref } from 'vue';
import LeftFile from './commponent/LeftFile.vue';
import RightFile from './commponent/RightFile.vue';
import { useUserStore } from '/@/store/modules/user';
import { useRouter } from 'vue-router';
const userStore = useUserStore();
const keyword = ref<string>('');
const rightFileRef = ref();
const parentId = ref<string>();
const showAllTitle = ref<boolean>(false);
const typeData = ref();
const firstPid = ref<string>('');
const firstFileName = ref<string>('');
const titleData = ref<any>([]);
const recoveryStation = ref<boolean>(false);
const type = ref<string>('');
const recycleFileId = ref<string>('');
const title = ref<string>('');
const leftFileRef = ref();
const registerModal = ref();
const leftType = ref<string>('');
/**
 * 查询右侧的列表
 * @param value
 */
function searchList(value) {

  keyword.value = value;
  // rightFileRef.value.searchList(value, unref(parentId));
  // if (!value) {
  //   showAllTitle.value = false;
  // }
}
function updateType(value) {
 
  typeData.value = value;
}
/**
 * 点击类别回调事件
 * @param data
 */
// function clickFileType(data) {
//   firstPid.value = data.firstId;
//   firstFileName.value = data.fileName;
//   title.value = data.fileName;
//   parentId.value = data.parentId;
//   titleData.value = [];

//   //是否为回收站
//   recoveryStation.value = false;
//   //用于区分我的文件和个人公司，我的文件type为不空，其余为空
//   type.value = data.type;
//   //左侧类别星标文件/最近使用/我的文件
//   leftType.value = data.leftType;
//   keyword.value = '';
//   showAllTitle.value = false;
//   //获取权限
//   getAuth({ fileId: data.firstId, userId: userStore.getUserInfo.id }).then((res) => {
//     if (res.success) {
//       authority.value = res.result;
//       rightFileRef.value.loadFileList({
//         pid: parentId.value,
//         firstPid: firstPid.value,
//         fileName: titleData.value,
//         firstTitle: title.value,
//         type: data.type,
//         authority: res.result,
//         isShare: unref(isShare),
//       });
//       leftFileRef.value.clearIds();
//     }
//   });
// }
</script>

<style scoped>
.bodyq {
  display: flex;
}
</style>