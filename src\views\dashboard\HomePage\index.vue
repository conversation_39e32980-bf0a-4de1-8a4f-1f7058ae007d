<!--
- @Name HomePage
- @Description：统一门户首页
- <AUTHOR>
- @Email: <EMAIL>
- @Date: 2023/9/4 9:26
- @FilePath:src\views\dashboard\HomePage\index.vue
-->
<script lang="ts" name="home-page">
  import { defineComponent, ref, watch, reactive } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  // import HomePageHeader from '/@/views/dashboard/HomePage/components/HomePageHeader.vue';
  // import CorporateNews from './components/CorporateNews.vue';
  // import NoticeAnnouncement from './components/NoticeAnnouncement.vue';
  // import CommonApplications from './components/commonApplications/CommonApplications.vue';
  // import Schedule from './components/schedule/Schedule.vue';
  import { getSysUserResourceLibrary, postSysUserResourceLibrarySave } from '@/api/dashboard/resourceLibrary';
  import { message, type TreeProps } from 'ant-design-vue';
  import NavigationVue from './components/navigationVue.vue';
  import MenuSetting from './components/menuSetting.vue';
  import ScheduleVue from './components/scheduleVue.vue';
  import NewListVue from './components/newListVue.vue';
  import NoticeVue from './components/noticeVue.vue';
  import PersonalVue from './components/personalVue.vue';
  import QuickAccessVue from './components/quickAccessVue.vue';
  import KnowledgeBaseVue from './components/knowledgeBaseVue.vue';
  import HelpCenterVue from './components/helpCenterVue.vue';
  import ContactUsVue from './components/contactUsVue.vue';
  import CarouselTabVue from './components/carouselTabVue.vue';
  import { usePermissionStore } from '/@/store/modules/permission';
  import _ from 'lodash-es';
  const permissionStore = usePermissionStore();
  console.log('权限问题', permissionStore);

  const useTreeComp = (treeData: TreeProps['treeData']) => {
    const expandedKeys = ref<string[]>(['0-0']);
    const selectedKeys = ref<string[]>();
    const checkedKeys = ref<string[]>();
    watch(expandedKeys, () => {
      console.log('expandedKeys', expandedKeys);
    });
    watch(selectedKeys, () => {
      console.log('selectedKeys', selectedKeys);
    });
    watch(checkedKeys, () => {
      console.log('checkedKeys', checkedKeys);
    });
    const handleExpand = (keys: string[], { expanded, node }) => {
      // node.parent add from 3.0.0-alpha.10
      const tempKeys = ((node.parent ? node.parent.children : treeData) || []).map(({ key }) => key);
      if (expanded) {
        expandedKeys.value = _.difference(keys, tempKeys).concat(node.key);
      } else {
        expandedKeys.value = keys;
      }
    };
    return {
      treeData,
      expandedKeys,
      selectedKeys,
      checkedKeys,
      handleExpand,
    };
  };

  export default defineComponent({
    components: {
      PageWrapper,
      // HomePageHeader,
      // CorporateNews,
      // NoticeAnnouncement,
      // CommonApplications,
      // Schedule,
      NavigationVue,
      MenuSetting,
      ScheduleVue,
      NewListVue,
      NoticeVue,
      PersonalVue,
      QuickAccessVue,
      KnowledgeBaseVue,
      HelpCenterVue,
      ContactUsVue,
      CarouselTabVue,
    },
    setup() {
      const onEdit = () => {
        visible.value = true;
      };

      // modal
      const visible = ref(false);

      watch(visible, () => {
        if (visible.value) {
          selectedKeys.value = [curItem.value.id];
        }
      });

      const handleOk = () => {
        if (!(selectItem.value && selectItem.value.id)) {
          return message.error('请选择对应的资源~');
        }
        const req = {
          appClass: selectItem.value.resourceClass + '',
          resourceId: selectItem.value.id,
        };
        postSysUserResourceLibrarySave(req).then(() => {
          visible.value = false;
          getResourceLibraryList();
        });
      };

      // tree
      const treeData: TreeProps['treeData'] = reactive([
        {
          title: '智能决策系统',
          key: '0-0',
          selectable: false,
          children: [],
        },
      ]);

      // 展示项目
      const curItem = ref({ link: '' });
      // 选择项目
      const selectItem = ref({ link: '' });

      const onSelect = (selectedKeys, e: { selected: boolean; selectedNodes; node; event }) => {
        selectItem.value = e.node.item || {};
      };

      // 获取tree数据
      const { selectedKeys, ...treeRest } = useTreeComp(treeData);
      const getResourceLibraryList = () => {
        const req = {
          resourceClass: 1, // （1：PC，2：APP）
          status: 1, // (0未发布1发布)
        };
        getSysUserResourceLibrary(req).then((res: any[]) => {
          // console.log(res,"getSysUserResourceLibrary")
          //发布并且res中有display为true的第一个数据显示，res中没有display为true的直接使用res的第一个
          const filterArr = (res || []).filter((item) => item?.display);
          const showItem = filterArr?.length ? filterArr[0] : res[0];
          if (res?.length) {
            curItem.value = showItem;
            selectedKeys.value = [showItem?.id];
            selectItem.value = showItem;
          }
          const list = res.map((r) => {
            return {
              title: r.name,
              key: r.id,
              item: r,
            };
          });

          treeData[0].children = list;
        });
      };

      getResourceLibraryList();

      return {
        onEdit,
        visible,
        handleOk,
        selectedKeys,
        ...treeRest,
        curItem,
        selectItem,
        onSelect,
      };
    },
  });
</script>

<template>
  <PageWrapper>
    <a-row :gutter="[16, 16]" style="margin-bottom: 16px">
      <!-- 产品中心 -->
      <a-col :span="16">
        <NavigationVue />
      </a-col>
      <a-col :span="8">
        <MenuSetting />
      </a-col>
    </a-row>
    <!--<a-row :gutter="[16, 16]" style="margin-bottom: 16px">-->
    <!--  &lt;!&ndash; 智汇视界 &ndash;&gt;-->
    <!--  <CarouselTabVue />-->
    <!--</a-row>-->
    <a-row :gutter="[16, 16]" style="margin-bottom: 16px">
      <a-col :span="8">
        <!-- <a-col :span="24"> <PersonalVue /> </a-col> -->
        <!-- <a-col :span="24"> <NoticeVue /> </a-col> -->
        <NoticeVue />
      </a-col>
      <a-col :span="8">
        <ScheduleVue />
      </a-col>
      <a-col :span="8">
        <NewListVue />
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" style="margin-bottom: 16px">
      <a-col :span="16">
        <KnowledgeBaseVue />
      </a-col>
      <a-col :span="8">
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <HelpCenterVue />
          </a-col>
        </a-row>
        <!-- <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <ContactUsVue />
          </a-col>
        </a-row> -->
      </a-col>
    </a-row>
    <!-- <a-row :gutter="[16, 16]" style="margin-bottom: 16px"> -->
    <!-- 快捷访问 -->
    <!-- <a-col :span="24">
        <QuickAccessVue />
      </a-col> -->

    <!-- <a-col :span="16">
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <CorporateNews />
          </a-col>
          <a-col :span="24">
            <a-card class="custom-card" title="重点业务概览">
              <template #extra><a href="#" @click="onEdit">自定义</a></template>
              <div style="height: 982px">
                <iframe v-if="curItem.link" style="width: 100%; height: 100%" :src="curItem.link"></iframe>
                <a-empty v-else description="暂未配置相关资源" />
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="8">
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <NoticeAnnouncement />
          </a-col>
          <a-col :span="24">
            <Schedule />
          </a-col>
          <a-col :span="24">
            <CommonApplications />
          </a-col>
        </a-row>
      </a-col> -->
    <!-- </a-row> -->
  </PageWrapper>
</template>

<style lang="less">
  .mt16 {
    margin-top: 16px;
  }
  .custom-card {
    border-radius: 10px; /* Change the border radius to your desired value */
  }
  .custom-card {
    border-radius: 10px; /* Change the border radius to your desired value */
  }
  :deep(.ant-page-header) {
    height: 100px;
    background-image: url('/@/assets/images/home_page_head_bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center left;
    margin: 16px;
    border-radius: 6px; /* Change the border radius to your desired value */
    padding: 0;
    display: flex;
    align-items: center;
    .ant-page-header-content {
      padding-top: 0;
    }
  }

  .modal-box {
    width: 80%;
    height: 80%;
  }
  .modal-content {
    margin: 12px;
    .title {
      font-size: 16px;
      padding: 5px;
    }
    .content {
      display: flex;
    }
  }
</style>
