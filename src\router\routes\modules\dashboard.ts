import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const dashboard: AppRouteModule = {
  path: '/dashboard',
  name: 'Dashboard',
  // path: '/portal',
  // name: '首页',
  component: LAYOUT,
  redirect: '/dashboard/analysis',
  meta: {
    orderNo: 10,
    icon: 'ion:grid-outline',
    title: t('routes.dashboard.dashboard'),
    breadcrumbName: '统一门户',
  },
  children: [
    {
      path: 'analysis',
      name: 'Analysis',
      component: () => import('/@/views/dashboard/HomePage/index.vue'),
      meta: {
        // affix: true,
        title: t('routes.dashboard.homepage'),
      },
    },
    {
      path: '/notice',
      name: '公告',
      component: () => import('/@/views/dashboard/notice/index.vue'),
      meta: {
        // affix: true,
        title: '通知公告',
        breadcrumbName: '通知公告',
      },
    },
    {
      path: '/calendarDetails/:date?',
      name: '日历详情列表',
      component: () => import('/@/views/dashboard/calendarDetails/index.vue'),
      meta: {
        // affix: true,
        title: '日历详情列表',
        breadcrumbName: '日程日志详情',
      },
    },
    // {
    //   path: '/calendarDetails',
    //   name: '日历详情列表',
    //   component: () => import('/@/views/dashboard/calendarDetails/index.vue'),
    //   meta: {
    //     // affix: true,
    //     title: '日历详情列表',
    //     breadcrumbName: '日程日志详情',
    //   },
    // },
    {
      path: '/knowledgeBase',
      name: '知识库',
      component: () => import('/@/views/dashboard/knowledgeBase/index.vue'),
      meta: {
        // affix: true,
        title: '知识库',
        breadcrumbName: '知识库',
      },
    },
    {
      path: '/helpCenter',
      name: '帮助中心',
      component: () => import('/@/views/dashboard/helpCenter/index.vue'),
      meta: {
        // affix: true,
        title: '帮助中心',
        breadcrumbName: '帮助中心',
      },
    },
    {
      path: '/system/usersetting',
      name: '用户设置',
      component: () => import('/@/views/dashboard/usersetting/UserSetting.vue'),
      meta: {
        title: '用户设置',
        breadcrumbName: '用户设置',
      },
    },
    // {
    //   path: 'workbench',
    //   name: 'Workbench',
    //   component: () => import('/@/views/dashboard/workbench/index.vue'),
    //   meta: {
    //     title: t('routes.dashboard.workbench'),
    //   },
    // },
  ],
};

export default dashboard;
