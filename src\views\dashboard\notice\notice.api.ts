import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/sys/annountCement/list',
  queryById = 'sys/annountCement/queryById',
  getMyAnnouncementSend = '/sys/sysAnnouncementSend/getMyAnnouncementSend',
}
/**
 * 查询租户列表
 * @param params
 */
export const getList = (params) => {
  return defHttp.get({ url: Api.list, params });
};

/**
 * 查询租户列表详情
 * @param params
 */
export const queryById = (params) => {
  return defHttp.get({ url: Api.queryById, params });
};
/**
 * 查询租户列表
 * @param params
 */
export const getMyAnnouncementSend = (params) => {
  return defHttp.get({ url: Api.getMyAnnouncementSend, params });
};
