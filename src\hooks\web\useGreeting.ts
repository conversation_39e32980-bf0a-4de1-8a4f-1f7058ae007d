/**
 * @Name useGreeting
 * @Description：根据时间段获取问候语
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 16:14
 * @FilePath: src\hooks\web\useGreeting.ts
 */
import { reactive, ref, watch } from 'vue';
import useTimePeriod, { TimePeriod } from './useTimePeriod';

interface GreetingStrategy {
  (name: string): {
    title: string;
    content: string;
  };
}

const greetingStrategies: Record<TimePeriod, GreetingStrategy> = {
  [TimePeriod.EarlyMorning]: (name: string) => ({
    title: `凌晨，${name}！`,
    content: '夜深人静，愿您在这宁静的时刻，享受属于自己的宁静时光！'
  }),
  [TimePeriod.Morning]: (name: string) => ({
    title: `上午好，${name}！`,
    content: '新的一天开始了，愿您充满活力，迎接新的挑战！'
  }),
  [TimePeriod.LateMorning]: (name: string) => ({
    title: `中午好，${name}！`,
    content: '午餐时间到了，吃饱喝足，继续加油哦！'
  }),
  [TimePeriod.Noon]: (name: string) => ({
    title: `午安，${name}！`,
    content: '午后的阳光温暖舒适，希望您度过一个愉快的下午！'
  }),
  [TimePeriod.Afternoon]: (name: string) => ({
    title: `下午好，${name}！`,
    content: '一天的工作已经过了一半，继续保持努力，完成今天的任务！'
  }),
  [TimePeriod.Evening]: (name: string) => ({
    title: `晚上好，${name}！`,
    content: '一天的工作已经结束，放松一下，享受美好的夜晚吧！'
  }),
};

export default function useGreeting(name: string) {
  const { timePeriod } = useTimePeriod();
  const greeting = reactive<{
    title: string;
    content: string;
  }>({ title: '', content: '' });

  function getGreeting() {
    greeting.content = greetingStrategies[timePeriod.value](name).content;
    greeting.title = greetingStrategies[timePeriod.value](name).title;
  }

  watch(() => name, () => {
    getGreeting();
  });
  getGreeting();

  return {
    greeting,
  };
}
