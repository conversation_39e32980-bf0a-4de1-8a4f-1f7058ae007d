/**
 * @Name scheduleModel
 * @Description：日程安排
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 17:16
 * @FilePath: src\api\dashboard\model\scheduleModel.ts
 */

import { BasicDataResult, BasicPageParams, BasicResult } from '/@/api/model/baseModel';

export type ScheduleParams = BasicPageParams & ScheduleListItem;

export interface ScheduleListItem {
  // 日程地址
  addr?: string;
  // 结束时间
  endTime?: string;
  // 日程事项
  item?: string;
  // 是否通知
  notify?: string;
  // 发布时间
  sendTime?: string;
  // 开始时间
  startTime?: string;
  // 状态
  status?: number;
  // 标题
  title?: string;
  // 提醒时间
  warnTime?: number;
}

export interface WeekDaysItem {
  date: string;
  dayOfMonth: string;
  dayOfWeek: string;
  existToDone: string;
}

export type ScheduleListGetResultModel = BasicResult<ScheduleListItem>
export type WeekDaysGetResultModel = BasicDataResult<WeekDaysItem>
