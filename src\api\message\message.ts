import { ipmsSaasHttp, ipmsSaasHttpLy1B, ipmsSaasHttpLy1C, ipmsSaasHttpLy5C } from '/@/utils/http/axios';
// import { getMenuListResultModel } from './model/menuModel';
const http = {
  ipms_ly1_a: ipmsSaasHttp,
  ipms_ly1_b: ipmsSaasHttpLy1B,
  ipms_ly1_c: ipmsSaasHttpLy1C,
  ipms_ly5_c: ipmsSaasHttpLy5C,
};

enum Api {
  GetMessageGroup = '/messages/getEventMessageGroup',
  GetMessagePage = '/messages/getEventMessagePage',
  UpdateReadFlag = '/messages/updateReadFlagForUser',
}

export const getMessageGroup: any = (data, type) => {
  if (type) {
    return new Promise((resolve) => {
      http[type].post({ url: Api.GetMessageGroup, data }).then((data) => {
        resolve(data);
      });
    });
  }
  return new Promise((resolve) => {
    ipmsSaasHttp.post({ url: Api.GetMessageGroup, data }).then((data) => {
      resolve(data);
    });
  });
};

export const getMessagePage: any = (data, type) => {
  if (type) {
    return new Promise((resolve) => {
      http[type].post({ url: Api.GetMessagePage, data }).then((data) => {
        resolve(data);
      });
    });
  }
  return new Promise((resolve) => {
    ipmsSaasHttp.post({ url: Api.GetMessagePage, data }).then((data) => {
      resolve(data);
    });
  });
};

export const updateReadFlag: any = (type) => {
  if (type) {
    return new Promise((resolve) => {
      http[type].post({ url: Api.UpdateReadFlag }).then((data) => {
        resolve(data);
      });
    });
  }
  return new Promise((resolve) => {
    ipmsSaasHttp.post({ url: Api.UpdateReadFlag }).then((data) => {
      resolve(data);
    });
  });
};
