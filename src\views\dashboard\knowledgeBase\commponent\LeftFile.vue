<template>
  <div class="left-content">
    <div class="file-search" :class="isShowBorderColor ? 'border-color' : ''">
      <Icon class="icon-search" icon="ant-design:search-outlined" />
      <a-input
        type="text"
        @click="isShowBorderColor = true"
        @blur="isShowBorderColor = false"
        class="file-search-box"
        :placeholder="placeholder"
        v-model:value="keyWord"
        @pressEnter="handlePressEnter"
      />
    </div>
    <ul class="left-file pointer">
      <!-- "我的文件" 相关的 <li> 已被移除 -->
      <li class="item" :class="type === 'used' ? 'item-active' : ''" @click="handleLiClick('used')">
        <Icon icon="ant-design:field-time-outlined" style="color: #00bcd4; font-size: 16px" />
        <span>最近</span>
      </li>
      <li class="item" :class="type === 'star' ? 'item-active' : ''" @click="handleLiClick('star')">
        <Icon icon="ant-design:star-outlined" style="color: #ff9800; font-size: 16px" />
        <span>星标</span>
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, unref, watch } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import { getFilesListApi, getFilesQueryByIdApi } from '../index.api';
const isShowBorderColor = ref<boolean>(false);
const placeholder = ref<string>('在“最近”中搜索');
const type = ref<string>('used');
const keyWord = ref<string>('');
const userStore = useUserStore();
//我的文件id
const myFileId = ref<string>('');
//我的文件设置按钮点击事件
const dropdownClick = ref<boolean>(false);
const emit = defineEmits(['register', 'success', 'click-file-type', 'recycle-upload-list', 'search-list', 'update-type']);
/**
 * 左侧菜单点击事件
 * @param value
 */
function handleLiClick(value) {
  type.value = value;
  emit('update-type', type.value);
  let fileName = '';
  if (value == 'used') {
    fileName = '最近';
  } else if (value == 'star') {
    fileName = '星标';
  }
  placeholder.value = '在' + '“' + fileName + '”中搜索';
  keyWord.value = '';
}
/**
 * 回车文件搜索
 */
function handlePressEnter() {
  emit('search-list', unref(keyWord));
}
/**
 * 我的文件鼠标移出事件
 */
function myFileMouseLeave() {
  if (!unref(dropdownClick)) {
    myFileSettingShow.value = false;
  }
}
//我的文件设置按钮是否隐藏
const myFileSettingShow = ref<boolean>(false);
/**
 * 设置我的文件下拉菜单是否隐藏
 */
watch(keyWord, (newVal, oldVal) => {
  handlePressEnter(); // 直接调用函数，或根据需要调整逻辑
});
</script>

<style scoped lang="less">
.left-file li {
  height: 40px !important;
  line-height: 40px !important;
  padding-left: 10px;
}

.left-file .item {
  &:hover {
    background: rgb(235, 235, 235);
  }
}
.item-active {
  background: #bbdefb;
}
.left-content {
  margin-right: 20px;
  float: left;
  width: 100%;
  background: white;
  border-right: 1px solid #e0e0e0;
  height: 100%;
  padding: 10px 10px;

  .item span {
    margin-left: 10px;
    font-size: 13px;
  }
}
.file-search {
  border-bottom: 1px solid rgba(0, 0, 0, 0.32) !important;
  box-sizing: border-box;
  height: 38px;
  margin: 6px auto;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
}
.file-search-box {
  border: none !important;
  height: 25px;
  line-height: 25px;
  margin-top: 7px;
  padding: 0 5px 0 40px;
  width: 100%;
  box-shadow: none !important;
}
.icon-search {
  display: inline-block;
  font-size: 20px;
  color: rgba(0, 0, 0, 0.54);
  position: absolute;
  z-index: 999;
  margin: 12px 9px 9px;
}
</style>