/**
 * 无界配置
 */
import <PERSON>jieVue from 'wujie-vue3';
import { apps , preloadApp} from './apps';
import { getProps } from './state';

import { useSso } from '/@/hooks/web/useSso';


const { setupApp, bus } = WujieVue;
/**
 * 重构apps
 */
function filterApps() {
  apps.forEach((app) => {
    //主应用需要传递给微应用的数据。
    app.props = {
      name:app.name,
      ...getProps()
    }
  });
  return apps;
}

/**
 * 微应用注册
 */
export function registerApps() {
  const _apps = filterApps();
  _apps.map((_app) => {
    // 安装应用
    setupApp(_app as any);
  });
  // 预加载应用
  preloadApp(0);

  // 主子应用通信-退出
  bus.$on("sso-logout", () => {
    useSso().ssoLoginOut();
  });
}


