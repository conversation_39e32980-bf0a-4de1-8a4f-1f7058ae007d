import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/sys/sysSchedule/scheduleAndLog/list',
  exportXls = '/sys/sysSchedule/scheduleAndLog/exportXls',
  scheduleEdit = '/sys/sysSchedule/edit',
  delete = '/sys/sysSchedule/delete',
  getDetailById = '/sys/sysSchedule/queryById',
  workLogEdit = '/sys/sysWorkLog/edit',
  workLogDelete = '/sys/sysWorkLog/delete',
  workLogGetDetailById = '/sys/sysWorkLog/queryById',
}

/**
 * 导出url
 */
export const getExportUrl = Api.exportXls;
/**
 * 查询租户列表
 * @param params
 */
export const getList = (params) => {
  return defHttp.get({ url: Api.list, params });
};
/**
 * 通过id查询
 * @param params
 */
export const getDetailById = (params, isWorkLog?) => {
  let url = isWorkLog ? Api.workLogGetDetailById : Api.getDetailById;
  return defHttp.get({ url: url, params });
};

/**
 * 删除
 * @param params
 */
export const deleteOne = (params, handleSuccess, isWorkLog) => {
  let url = isWorkLog ? Api.workLogDelete : Api.delete;
  return defHttp.delete({ url: url, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 保存或者更新通告
 * @param params
 */
export const update = (params, isWorkLog) => {
  let url = isWorkLog ? Api.workLogEdit : Api.scheduleEdit;
  return defHttp.post({ url: url, params });
};
