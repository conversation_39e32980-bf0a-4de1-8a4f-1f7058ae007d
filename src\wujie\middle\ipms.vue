<template>
  <WujieVue width="100%" height="100%" :name="name" :plugins="plugins" :props="props" />
</template>
<script>

import { useRoute } from 'vue-router'
import { getProps } from '@/wujie/state';

const props = getProps()

const plugins = [
  {
    jsBeforeLoaders: [
      {
        callback: (appWindow) => {
          // appWindow.parent.document.exitFullscreen = appWindow.document.exitFullscreen
          Object.defineProperties(appWindow.document, {
            exitFullscreen: {
              get: () => {
                const res = appWindow.__WUJIE.degrade
                  ? appWindow.__WUJIE.document.exitFullscreen
                  : appWindow.parent.exitFullscreen;
                return res
              },
              set: (value) => {
                console.log(value)
                return value
              }
            }
          });
        },
      },
    ],
  },
]

export default {
  data() {
    return {
      name: 'ipms',
      plugins,
      props
    };
  },
  activated() {
    console.log('activated')
  },
  mounted() {
    const route = useRoute()
    this.name = route.path.split('middle/')[1]
    this.props.name = this.name;
    console.log('mounted', route, this.props);
  }
};
</script>