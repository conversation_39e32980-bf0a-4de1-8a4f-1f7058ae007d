import { tempHttp } from '/@/utils/http/axios';

enum Api {
  list = '/temp/alarmMessage/list',
  confirmAll = '/temp/alarmMessage/confirmSelf',
  typeCount = '/temp/alarmMessage/type/count',
  confirm = '/temp/alarmMessage/confirm',
}

/**
 * 告警消息-分页列表查询
 * @param params
 */
export function getList(params) {
  return tempHttp.get({ url: Api.list, params });
}

/**
 * 告警消息-一键确认
 * @param params
 */
export function confirmAll(params) {
  return tempHttp.post({ url: Api.confirmAll, params });
}

/**
 * /temp/alarmMessage/type/count
 */
export function typeCount(params) {
  return tempHttp.get({ url: Api.typeCount, params });
}

/**
 * 告警消息-确认
 * @param params
 */
export const confirm = (params) => {
  return tempHttp.post({
    url: Api.confirm,
    params,
  });
};
