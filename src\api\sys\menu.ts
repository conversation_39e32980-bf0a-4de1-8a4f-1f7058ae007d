import { defHttp } from '/@/utils/http/axios';
import { getMenuListResultModel } from './model/menuModel';

enum Api {
  GetAppList = '/sys/app/myAll',
  GetMenuList = '/sys/permission/getUserPermissionByToken',
  SwitchVue3Menu = '/sys/switchVue3Menu',
}

/**
 * @description: Get user applist based on token
 */
export const getAppList: any = (params) => {
  return new Promise((resolve) => {
    defHttp.get({ url: Api.GetAppList, params }).then((res) => {
      // const list = Object.keys(res).map(r => {
      //   const menu = res[r].menu
      //   const route = menu.filter(r => r.path === '/isystem')
      //   return route[0] || menu[0]
      // })
      resolve(res || [])

    });
  });
};

/**
 * @description: Get user menu based on id
 */
export const getMenuList = () => {
  return new Promise((resolve) => {
    //为了兼容mock和接口数据
    defHttp.get<getMenuListResultModel>({ url: Api.GetMenuList }).then((res) => {
      console.log('res:', res);
      if (Array.isArray(res)) {
        resolve(res);
      } else {
        resolve(res['menu']);
      }
    });
  });
};


/**
 * 切换成vue3菜单
 */
export const switchVue3Menu = () => {
  return new Promise((resolve) => {
    defHttp.get({ url: Api.SwitchVue3Menu });
  });
};
