/*
 * @Description:
 * @Autor: yst
 * @Date: 2024-10-22 08:22:45
 * @LastEditors: yst
 * @LastEditTime: 2024-10-22 12:55:12
 */
import { UploadApiResult } from './model/uploadModel';
import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { uploadUrl } from '/@/api/common/api';
// const { uploadUrl = '' } = useGlobSetting();

export function uploadHandle(params) {
  return defHttp.post<UploadApiResult>(
    {
      url: uploadUrl,
    },
    params
  );
}
/**
 * @description: Upload interface
 */
export function uploadApi(params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) {
  return defHttp.uploadFile<UploadApiResult>(
    {
      url: uploadUrl,
      onUploadProgress,
    },
    params
  );
}
/**
 * @description: Upload interface
 */
export function uploadImg(params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) {
  return defHttp.uploadFile<UploadApiResult>(
    {
      url: `${uploadUrl}/sys/common/upload`,
      onUploadProgress,
    },
    params,
    { isReturnResponse: true }
  );
}
