<template>
  <Dropdown placement="bottomLeft" :overlayClassName="`${prefixCls}-dropdown-overlay`">
    <span :class="[prefixCls, `${prefixCls}--${theme}`]" class="flex">
      <img :class="`${prefixCls}__header`" :src="getAvatarUrl" />
      <span :class="`${prefixCls}__info hidden md:block`">
        <span :class="`${prefixCls}__name  `" class="truncate">
          {{ getUserInfo.realname }}
          <DownOutlined />
        </span>
      </span>
      <img style="margin: 0 10px" :src="caret_down" alt="更多" />
    </span>
    <template #overlay>
      <Menu @click="handleMenuClick">
        <!--        <MenuItem key="doc" :text="t('layout.header.dropdownItemDoc')" icon="ion:document-text-outline" v-if="getShowDoc" />-->
        <!--        <MenuDivider v-if="getShowDoc" />-->
        <!-- <MenuItem key="account" :text="t('layout.header.dropdownItemSwitchAccount')" icon="ant-design:setting-outlined" /> -->
        <MenuItem key="password" :text="t('layout.header.dropdownItemSwitchPassword')" icon="ant-design:edit-outlined" />
        <MenuItem key="system" text="系统管理" icon="ant-design:setting-outlined" />
        <MenuItem v-if="getUserCorp && getUserCorp.length > 1" key="depart" :text="t('layout.header.dropdownItemSwitchTenant')" icon="ant-design:cluster-outlined" />
        <MenuItem key="cache" :text="t('layout.header.dropdownItemRefreshCache')" icon="ion:sync-outline" />
        <!-- <MenuItem
            v-if="getUseLockPage"
            key="lock"
            :text="t('layout.header.tooltipLock')"
            icon="ion:lock-closed-outline"
        /> -->
        <MenuItem key="logout" :text="t('layout.header.dropdownItemLoginOut')" icon="ion:power-outline" />
      </Menu>
    </template>
  </Dropdown>
  <LockAction @register="register" />
  <DepartSelect ref="loginSelectRef" />
  <UpdatePassword ref="updatePasswordRef" />
</template>
<script lang="ts">
  // components
  import { Dropdown, Menu, message } from 'ant-design-vue';

  import { computed, defineComponent, ref } from 'vue';

  import { SITE_URL } from '/@/settings/siteSetting';

  import { useUserStore } from '/@/store/modules/user';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/src/hooks/web/useMessage';
  import { useGo } from '/@/hooks/web/usePage';
  import caret_down from '/@/assets/images/caret-down.png';
  import avatar_female from '/@/assets/images/avatar_female.png';
  import avatar_male from '/@/assets/images/avatar_male.png';
  import { propTypes } from '/@/utils/propTypes';
  import { openWindow } from '/@/utils';

  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

  import { queryAllDictItems, refreshCache } from '/@/views/system/dict/dict.api';
  import { DB_DICT_DATA_KEY } from '/src/enums/cacheEnum';
  import { removeAuthCache, setAuthCache } from '/src/utils/auth';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

  // 系统管理菜单权限
  import { usePermissionStore } from '/@/store/modules/permission';

  const { t } = useI18n();
  type MenuEvent = 'logout' | 'doc' | 'lock' | 'cache' | 'depart';
  const { createMessage } = useMessage();
  export default defineComponent({
    name: 'UserDropdown',
    components: {
      Dropdown,
      Menu,
      MenuItem: createAsyncComponent(() => import('./DropMenuItem.vue')),
      MenuDivider: Menu.Divider,
      LockAction: createAsyncComponent(() => import('../lock/LockModal.vue')),
      DepartSelect: createAsyncComponent(() => import('./DepartSelect.vue')),
      UpdatePassword: createAsyncComponent(() => import('./UpdatePassword.vue')),
    },
    props: {
      theme: propTypes.oneOf(['dark', 'light']),
    },
    setup() {
      const permissionStore = usePermissionStore();
      let isShowRouter;
      let routerList = permissionStore.$state.backMenuList;
      function navRoute(item) {
        isShowRouter = routerList.some((o) => o.path == item);
        if (isShowRouter) {
          // const key = item.replace(/\/middle\/(.*)/,'$1')
          console.log('---------- portal to child item:', item);
          go(item);
          // updateHistory(key)
        } else {
          // console.log('得到');
          message.warning('暂无权限访问，可联系管理员配置');
        }
      }

      const { prefixCls } = useDesign('header-user-dropdown');
      const { t } = useI18n();
      const { getShowDoc, getUseLockPage } = useHeaderSetting();
      const userStore = useUserStore();
      const newAvatar = ref();
      const go = useGo();

      const getUserInfo = computed(() => {
        const { realname = '', avatar, desc, sex = null } = userStore.getUserInfo || {};
        if (avatar) {
          newAvatar.value = avatar;
        } else {
          if (sex == 2) {
            newAvatar.value = avatar_female;
          } else {
            newAvatar.value = avatar_male;
          }
        }
        return { realname, avatar: newAvatar.value, desc };
      });

      const getUserCorp = computed(() => {
        return userStore.getUserCorp || [];
      });

      const getAvatarUrl = computed(() => {
        console.log(getUserInfo.value, 789789789798789);
        let { avatar } = getUserInfo.value;
        if (avatar == newAvatar.value) {
          return avatar;
        } else {
          return getFileAccessHttpUrl(avatar);
        }
      });

      const [register, { openModal }] = useModal();
      /**
       * 多部门弹窗逻辑
       */
      const loginSelectRef = ref();
      function handleLock() {
        openModal(true);
      }

      //  login out
      function handleLoginOut() {
        userStore.confirmLoginOut();
      }

      // open doc
      function openDoc() {
        openWindow(SITE_URL);
      }

      // 清除缓存
      async function clearCache() {
        const result = await refreshCache();
        if (result.success) {
          const res = await queryAllDictItems();
          removeAuthCache(DB_DICT_DATA_KEY);
          setAuthCache(DB_DICT_DATA_KEY, res.result);
          createMessage.success('刷新缓存完成！');
        } else {
          createMessage.error('刷新缓存失败！');
        }
      }
      // 切换部门
      function updateCurrentDepart() {
        loginSelectRef.value.show();
      }
      // 修改密码
      const updatePasswordRef = ref();
      function updatePassword() {
        updatePasswordRef.value.show(userStore.getUserInfo.username);
      }

      function handleMenuClick(e: { key: MenuEvent }) {
        // console.log(e, 55555, e.key);
        // if (e.key == 'logout') {
        //   Modal.confirm({
        //     iconType: 'warning',
        //     title: t('sys.app.logoutTip'),
        //     content: t('sys.app.logoutMessage'),
        //     closable: true,
        //     maskClosable: true,
        //     onOk: async () => {
        //       console.log('确定');
        //       // await this.logout(true);
        //     },
        //     onCancel: () => {
        //       console.log('取消');
        //     },
        //   });
        // }
        switch (e.key) {
          case 'logout':
            handleLoginOut();
            break;
          case 'doc':
            openDoc();
            break;
          case 'lock':
            handleLock();
            break;
          case 'cache':
            clearCache();
            break;
          case 'depart':
            updateCurrentDepart();
            break;
          case 'password':
            updatePassword();
            break;
          case 'account':
            //update-begin---author:wangshuai ---date:********  for：进入用户设置页面------------
            go(`/system/usersetting`);
          //update-end---author:wangshuai ---date:********  for：进入用户设置页面--------------
          case 'system':
            navRoute('/middle/system');
            break;
        }
      }

      return {
        prefixCls,
        t,
        getUserInfo,
        getAvatarUrl,
        getUserCorp,
        handleMenuClick,
        getShowDoc,
        register,
        getUseLockPage,
        loginSelectRef,
        updatePasswordRef,
        caret_down,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-user-dropdown';

  .@{prefix-cls} {
    height: @header-height;
    padding: 0 0 0 10px;
    padding-right: 10px;
    overflow: hidden;
    font-size: 12px;
    cursor: pointer;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }

    &__header {
      border-radius: 50%;
    }

    &__name {
      font-size: 14px;
    }

    &--dark {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    &--light {
      &:hover {
        background-color: @header-light-bg-hover-color;
      }

      .@{prefix-cls}__name {
        color: @text-color-base;
      }

      .@{prefix-cls}__desc {
        color: @header-light-desc-color;
      }
    }

    &-dropdown-overlay {
      .ant-dropdown-menu-item {
        min-width: 160px;
      }
    }
  }
</style>
