/**
 * Used to parse the .env.development proxy configuration
 */
import type { ProxyOptions } from 'vite';

type ProxyItem = [string, string];

type ProxyList = ProxyItem[];

type ProxyTargetList = Record<string, ProxyOptions>;

const httpsRE = /^https:\/\//;

/**
 * Generate proxy
 * @param list
 */
export function createProxy(list: ProxyList = []) {
  const ret: ProxyTargetList = {};
  for (const [prefix, target] of list) {
    const isHttps = httpsRE.test(target);

    // https://github.com/http-party/node-http-proxy#options
    ret[prefix] = {
      target: target,
      changeOrigin: true,
      ws: true,
      rewrite: (path) => path.replace(new RegExp(`^${prefix}`), ''),
      // https is require secure=false
      ...(isHttps ? { secure: false } : {}),
      onProxyRes: function (proxyRes, req, res) {
        if (req.method === 'OPTIONS') {
          proxyRes.headers['Access-Control-Allow-Origin'] = req.headers.origin || '*'
          proxyRes.headers['Access-Control-Allow-Credentials'] = true
          proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,POST,OPTIONS,PUT,DELETE,FETCH'
          // 这里的参数，根据自己项目增删
          proxyRes.headers['Access-Control-Allow-Headers'] = 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,token,source'
          proxyRes.statusCode = 204
        } else {
          proxyRes.headers['Access-Control-Allow-Origin'] = req.headers.origin || '*'
          proxyRes.headers['Access-Control-Allow-Credentials'] = true
        }
      }
    };
  }
  console.log('ret', ret)
  return ret;
}
