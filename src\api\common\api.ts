import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';

const globSetting = useGlobSetting();
const baseUploadUrl = globSetting.uploadUrl;
// /sys/user/getCurrentUserCorps 选公司的
// /sys/user/getCurrentUserFactorys 选工厂的
// /sys/app/indexShowApps 获取首页应用
// /sys/user/selectDepartment 选择部门
// /sys/user/getDepartmentId 获取选择部门id

enum Api {
  positionList = '/sys/position/list',
  userList = '/sys/user/list',
  roleList = '/sys/role/list',
  feedback = '/sys/opinion/add',
  queryDepartTreeSync = '/sys/sysDepart/queryDepartTreeSync',
  queryTreeList = '/sys/sysDepart/queryTreeList',
  loadTreeData = '/sys/category/loadTreeData',
  loadDictItem = '/sys/category/loadDictItem/',
  getDictItems = '/sys/dict/getDictItems/',
  getTableList = '/sys/user/queryUserComponentData',
  getCategoryData = '/sys/category/loadAllData',
  getVersion = '/sys/online/getVersion',
  openSession = '/sys/intelligentAIAssistant/openSession',
  closeSession = '/sys/intelligentAIAssistant/closeSession',
  getCurrentUserCorps = '/sys/user/getCurrentUserCorps',
  getCurrentUserFactorys = '/sys/user/getCurrentUserFactorys',
  getCurrentUserFactoryApps = '/sys/user/getCurrentUserFactoryApps',
  getFactoryApp = '/sys/app/getFactoryApp',
  indexShowApps = '/sys/app/indexShowApps',
  isPermission = '/sys/app/isPermission',
  selectDepartment = '/sys/user/selectDepartment',
  getDepartmentId = '/sys/user/getDepartmentId',
}

/**
 * 上传父路径
 */
export const uploadUrl = `${baseUploadUrl}/system/api/sys/common/upload`;

/**
 * 职务列表
 * @param params
 */
export const sendFeedback = (params) => {
  return defHttp.post({ url: Api.feedback, params });
};

export const getPositionList = (params) => {
  return defHttp.get({ url: Api.positionList, params });
};
/**
 * 用户列表
 * @param params
 */
export const getUserList = (params) => {
  return defHttp.get({ url: Api.userList, params });
};

/**
 * 角色列表
 * @param params
 */
export const getRoleList = (params) => {
  return defHttp.get({ url: Api.roleList, params });
};

/**
 * 异步获取部门树列表
 */
export const queryDepartTreeSync = (params?) => {
  return defHttp.get({ url: Api.queryDepartTreeSync, params });
};
/**
 * 获取部门树列表
 */
export const queryTreeList = (params?) => {
  return defHttp.get({ url: Api.queryTreeList, params });
};

/**
 * 分类字典树控件 加载节点
 */
export const loadTreeData = (params?) => {
  return defHttp.get({ url: Api.loadTreeData, params });
};

/**
 * 根据字典code加载字典text
 */
export const loadDictItem = (params?) => {
  return defHttp.get({ url: Api.loadDictItem, params });
};

/**
 * 根据字典code加载字典text
 */
export const getDictItems = (dictCode) => {
  return defHttp.get({ url: Api.getDictItems + dictCode }, { joinTime: false });
};
/**
 * 部门用户modal选择列表加载list
 */
export const getTableList = (params) => {
  return defHttp.get({ url: Api.getTableList, params });
};
/**
 * 加载全部分类字典数据
 */
export const getVersion = (params = {}) => {
  return defHttp.get({ url: Api.getVersion, params });
};

export const loadCategoryData = (params) => {
  return defHttp.get({ url: Api.getCategoryData, params });
};
/**
 * 文件上传
 */
export const uploadFile = (params, success) => {
  return defHttp.uploadFile({ url: uploadUrl }, params, { success });
};
/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export const downloadFile = (url, fileName?, parameter?) => {
  return getFileblob(url, parameter).then((data) => {
    if (!data || data.size === 0) {
      message.warning('文件下载失败');
      return;
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([data]), fileName);
    } else {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    }
  });
};

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export const getFileblob = (url, parameter) => {
  return defHttp.get(
    {
      url: url,
      params: parameter,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};

/**
 * 【用于评论功能】自定义文件上传-方法
 */
export const uploadMyFile = (url, data) => {
  return defHttp.uploadMyFile(url, data);
};

/**
 * 打开会话
 * @param params
 */
export const openSession = () => {
  return defHttp.post({ url: Api.openSession });
};

/**
 * 关闭会话
 * @param params
 */
export const closeSession = (params = {}) => {
  return defHttp.post({ url: Api.closeSession, params });
};

/**
 * 获取当前用户公司
 * @param params
 */
export const getCurrentUserCorps = (params?) => {
  return defHttp.get({ url: Api.getCurrentUserCorps, params });
};
/**
 * 获取当前用户工厂
 * @param params
 */
export const getCurrentUserFactorys = (params) => {
  return defHttp.get({ url: Api.getCurrentUserFactorys, params });
};
/**
 * 获取当前用户工厂应用
 * @param params
 */
export const getCurrentUserFactoryApps = (params) => {
  return defHttp.get({ url: Api.getCurrentUserFactoryApps, params });
};
/**
 * 获取当前用户所选厂应用
 * @param params
 */
export const getFactoryApp = (params) => {
  return defHttp.get({ url: Api.getFactoryApp, params });
};
/**
 * 获取首页应用
 * @param params
 */
export const indexShowApps = (params) => {
  return defHttp.get({ url: Api.indexShowApps, params });
};
/**
 * 选择部门
 * @param params
 */
export const selectDepartment = (params) => {
  return defHttp.get({ url: Api.selectDepartment, params });
};
/**
 * 判断用户是否有应用权限
 * @param params
 */
export const isPermission = (params) => {
  return defHttp.get({ url: Api.isPermission, params });
};
/**
 * 获取选择部门id
 * @param params
 */
export const getDepartmentId = () => {
  return defHttp.get({ url: Api.getDepartmentId });
};
