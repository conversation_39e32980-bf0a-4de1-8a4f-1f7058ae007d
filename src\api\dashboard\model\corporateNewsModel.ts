/**
 * @Name corporateNewsModel
 * @Description：企业新闻
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/5 17:47
 * @FilePath: src\api\dashboard\model\corporateNewsModel.ts
 */
import { BasicPageParams, BasicRowsResult } from '/@/api/model/baseModel';

export type NewsParams = BasicPageParams;

export interface NewsListItem {
  //概述
  descContent: string;
  // 新闻链接
  link: string;
  // 图片地址
  picturePath: string;
  // 发布时间
  sendTime: string;
  // 标题
  title: string;
}

export type NewsListGetResultModel = BasicRowsResult<NewsListItem>
