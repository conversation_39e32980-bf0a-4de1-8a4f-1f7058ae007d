import { defHttp } from "@/utils/http/axios";
import { getToken } from '/@/utils/auth';
let token = '';

enum Api {
  saveVisitLog = '/sys/sysPermissionVisitLog/saveVisitLog',
}

export const saveAccessLogRecord = (params) => {
  defHttp.get({ url: Api.saveVisitLog, params }, { errorMessageMode: 'none', successMessageMode: 'none' }).then(() => {
    token = getToken();
  });
};

// export function saveAccessLogRecord(params: any, options: any = null) {
//   console.log('saveAccessLogRecord data:', params);
//   return request<API.ResultArraylong>('/sys/sysPermissionVisitLog/saveVisitLog', {
//     method: 'GET',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     params,
//     ...(options || {}),
//     errorHandler: () => {},
//   }).then(() => {
//     token = webStorage.get(S_TOKEN);
//   });
//   // navigator.sendBeacon(`/sys/sysPermissionVisitLog/saveVisitLog?`);
// }

export function saveSccessLogRecordByFetch(params) {
  fetch(`/system/api/sys/sysPermissionVisitLog/saveVisitLog?id=${params.id}&endTime=${params.endTime}&permissionId=${params.permissionId}&startTime=${params.startTime}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'x-access-token': token
    },
    keepalive: true
  });
}
