<template>
  <div class="scheduleBox">
    <div class="smallTit" style="font-weight: bold"> 知识库 </div>
    <div class="knowledgeBox">
      <a-row :gutter="[16, 16]">
        <a-col :span="14">
          <div class="wenjk">
            <div class="wenjianTitle">
              <div class="leftTit">
                <div class="imgT">
                  <img :src="wenjian" alt="" />
                </div>
                <div class="titB">文件库</div>
              </div>
              <div class="moreWj" @click="handleKnowledge">更多 <right-outlined /> </div>
            </div>
            <div class="wenjianListBox">
              <div class="wenjianList" v-for="(item, index) in myKnowledge.list" :key="index" @click="getKnowInfo(item)">
                <div class="iconWj">
                  <img :src="wenzhang" alt="" />
                </div>
                <div class="infoBox">
                  <div class="infoTit">{{ item.fileName }}</div>
                  <!-- <div class="infoDec">{{ item.createTime }}</div> -->
                  <div class="zanKan">
                    <span>创建人：{{ item.createBy_dictText }}</span>
                    <span>时间：{{ item.createTime }} </span>
                    <!-- <span><Icon icon="solar:eye-outline" width="14" height="14" style="color: #4e5969" class="iconS" />{{ item.readCount }}</span> -->
                    <!-- <span><Icon icon="carbon:thumbs-up" width="14" height="14" style="color: #4e5969" class="iconS" />{{ item.downCount }}</span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="10">
          <div class="hyInfo">
            <div class="wenjianTitle">
              <div class="leftTit">
                <div class="imgT">
                  <img :src="hangye" alt="" />
                </div>
                <div class="titB">行业洞察</div>
              </div>
            </div>
            <div class="wenjianListBox">
              <div class="wenjianList" v-for="(item, index) in myKnowledge.list1" :key="index" @click="newInfo(item.link)">
                <div class="iconWj">
                  <img :src="wenzhang" alt="" />
                </div>
                <div class="infoBox">
                  <div class="infoTit">{{ item.title }}</div>
                  <!-- <div class="infoDec">{{ item.dec }}</div>
                  <div class="zanKan">
                    <span><Icon icon="solar:eye-outline" width="14" height="14" style="color: 4e5969" class="iconS" />{{ item.viewNum }}</span>
                    <span><Icon icon="carbon:thumbs-up" width="14" height="14" style="color: 4e5969" class="iconS" />{{ item.getNum }}</span>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { RightOutlined  } from '@ant-design/icons-vue';
  import wenzhang from '/@/assets/images/homeImg/wenzhang.png';
  import wenjian from '/@/assets/images/homeImg/wenjian.png';
  import hangye from '/@/assets/images/homeImg/hangye.png';
  import { getSysNewsList, getKnowledge } from '../index.api';
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const myKnowledge = reactive({
    list: [
      {
        id: 1,
        title: '电解铝工艺流程电解铝工艺流程电解铝工艺流程电解铝工艺流程电解铝工艺流程',
        dec: '随着各单位对创新技改工作重视程度不断提高，创新技改项目质量显著提高目质量显…',
        viewNum: 5648,
        getNum: 8542,
      },
      {
        id: 2,
        title: '2电解铝工艺流程',
        dec: '随着各单位对创新技改工作重视程度不断提高，创新技改项目质量显著提高目质量显…',
        viewNum: 5648,
        getNum: 8542,
      },
      {
        id: 3,
        title: '3电解铝工艺流程',
        dec: '随着各单位对创新技改工作重视程度不断提高，创新技改项目质量显著提高目质量显…',
        viewNum: 5648,
        getNum: 8542,
      },
    ],
    list1: [
      {
        id: 1,
        title: '电解铝工艺流程电解铝工艺流程电解铝工艺流程电解铝工艺流程电解铝工艺流程程2电解铝工艺流程',
      },
      {
        id: 2,
        title: '2电解铝工艺流程',
      },
      {
        id: 3,
        title: '3电解铝工艺流程',
      },
      {
        id: 4,
        title: '2电解铝工艺流程2电解铝工艺流程2电解铝工艺流程2电解铝工艺流程2电解铝工艺流程程2电解铝工艺流程',
      },
      {
        id: 5,
        title: '3电解铝工艺流程3电解铝工艺流程3电解铝工艺流程3电解铝工艺流程3电解铝工艺流程3电解铝工艺流程3电解铝工艺流程',
      },
    ],
  });
  //   点击跳转详情
  function getKnowInfo(item) {
    window.open(item.url);
  }
  //   文件库
  function getKnowledgeFun() {
    getKnowledge({ pageNo: 1, pageSize: 4, izRootFolder: 1, fileName: '**', column: 'createTime', order: 'desc' }).then((res) => {
      myKnowledge.list = res.records;
    });
  }
  //   行业洞察;
  function getNewList() {
    getSysNewsList({ type: '3', page: '1', pageSize: '5' }).then((res) => {
      myKnowledge.list1 = res.records;
    });
  }
  //   行业洞察
  function newInfo(item) {
    // router.push(item);
    window.open(item);
    // window.location.href = item;
  }
  onMounted(() => {
    getNewList();
    getKnowledgeFun();
  });
  //   点击我的日常跳转更多
  function handleKnowledge() {
    router.push('/knowledgeBase');
  }
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 500px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
    }
    .knowledgeBox {
      width: 100%;
      height: calc(100% - 48px);
      .wenjk {
        width: 100%;
        // height: 412px;
        background: #f8f9fa;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 20px;
        .wenjianTitle {
          width: 100%;
          height: 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 18px;
          .leftTit {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .imgT {
              width: 21px;
              height: 21px;
              margin-right: 14px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .titB {
              font-size: 22px;
              color: #000;
            }
          }
          .moreWj {
            font-size: 16px;
            color: #717376;
            cursor: pointer;
          }
        }
        .wenjianListBox {
          width: 100%;
          // height: 330px;
          .wenjianList {
            width: 100%;
            height: 82px;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            cursor: pointer;
            .iconWj {
              width: 21px;
              height: 21px;
              margin-right: 14px;
              margin-top: 5px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .infoBox {
              width: calc(100% - 35px);
              height: 310px;
              .infoTit {
                font-size: 18px;
                color: #000;
                line-height: 32px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .infoDec {
                font-size: 14px;
                color: #4e5969;
                line-height: 32px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }
              .zanKan {
                margin-top: 12px;
                font-size: 14px;
                line-height: 22px;
                color: #717376;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                span {
                  margin-right: 20px;
                  //   display: inline-block;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                  .iconS {
                    line-height: 22px;
                    margin-right: 5px;
                  }
                }
              }
            }
          }
        }
      }
      .hyInfo {
        width: 100%;
        height: 412px;
        background: #f8f9fa;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 20px;
        .wenjianTitle {
          width: 100%;
          height: 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 18px;
          .leftTit {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .imgT {
              width: 21px;
              height: 21px;
              margin-right: 14px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .titB {
              font-size: 22px;
              color: #000;
            }
          }
          .moreWj {
            font-size: 22px;
            color: #717376;
            cursor: pointer;
          }
        }
        .wenjianListBox {
          width: 100%;
          height: 330px;
          .wenjianList {
            width: 100%;
            // height: 60px;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            margin-bottom: 6px;
            cursor: pointer;
            .iconWj {
              width: 18px;
              height: 18px;
              margin-right: 8px;
              margin-top: 6px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .infoBox {
              width: calc(100% - 35px);
              // height: 62px;
              margin: 6px 0;
              .infoTit {
                font-size: 18px;
                color: #000;
                line-height: 28px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                display: -webkit-box;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
    }
  }
</style>
