<template>
    <el-popover
        :visible="visible" 
        placement="bottom-start" 
        title="" 
        :width="dialogWidth" 
        trigger="click"
        :visible-arrow="false" 
        :destroy-on-close="true"
        :popper-class="isLeft ? 'popoverLeft' : 'popoverRight'"
        @before-enter="onBeforeEnter"
        @before-leave="onBeforeLeave"
    >
        <template #reference>
            <draggable :distanceRight='0' :distanceBottom='100' :isScrollHidden='false' :isCanDraggable='true' :zIndex="100"
            @drag-start="onDragStart" @drag-end="onDragEnd" @drag-move="onDragMove" @get-position="handlePosition">
            <span class="ballWrap" @click.stop="onBallClick">
                <img class="ball" :src="ballImg" :width="90" :height="90" />
            </span>
            </draggable>
        </template>
        <div class="aiBox">
            <!-- <img v-show="visible" id="model" :src="guideUrl" :style="{
                width: modelheight * 0.5625 + 'px',
                height: modelheight + 'px',
            }"> -->
            <iframe id="dialog" class="dialogWrap" :width="dialogWidth" :height="800" :src="dialogUrl" />
        </div>
    </el-popover>
    <a-modal title="" :visible="visible" :width="0" :zIndex="998" :closable="false" :footer="null"
        @cancel="handleClose">
    </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted, defineEmits } from 'vue'
import { ElPopover } from 'element-plus'
import Draggable from './Draggable/Draggable.vue';
import { useMessage } from '/@/hooks/web/useMessage';
import ballImg from '@/assets/levitated_sphere.gif';
import { openSession, closeSession } from '@/api/common/api'
import { Modal, message } from 'ant-design-vue';
import { getToken } from '/@/utils/auth';
import WujieVue from 'wujie-vue3';
import { permissionId, collectLog, collectPrevLog } from '@/utils/accessLogUtil';

const { bus } = WujieVue;

const emit = defineEmits(['onReady']);

const visible = ref(false)
const isKeydown = ref(false)
const isDrag = ref(false)
const dialogUrl = ref(import.meta.env.VITE_APP_ENV === 'prod' ? 'https://ipms.corp.hongqiaocloud.com/voice-interation/index55_test4_https.html' : 'https://ipms.test.hongqiaocloud.com/voice-interation/index55_test4_https.html')
// const dialogUrl = ref('http://127.0.0.1:5500/index55_test4_https.html')
// const allow = ref("microphone; speaker; autoplay;");
const dialogWidth = ref(840);
const modelheight = ref(document.body.clientHeight - 32);
const isLeft = ref(false);
const isReady = ref(false); // 是否就绪
const showTip = ref(false);


watch(isReady, (val) => {
    emit('onReady', val)
});

watch(visible, (val) => {
    if (!val) {
        closeSession()   
    }
})


const handleOpenSession = (sendCheck: boolean = false) => {
    return openSession().then(data => {
        if (data === true) {
            visible.value = true;
            
            // 发送消息之前的校验
            if (sendCheck) {
                const iframe = document.getElementById('dialog');
                iframe.contentWindow.postMessage('send_approval', '*');
            }
        } else {
            // Modal.error({
            //     title: '当前在线人数较多，小智忙不过来了，请您稍后重试',
            //     // content: '',
            //     onOk(e) {
            //         console.log('ok?', e)
            //         e()
            //     }
            // });
            const { createErrorModal } = useMessage();
            createErrorModal({
                iconType: 'error',
                title: '提示',
                content: '当前在线人数较多，小智忙不过来了，请您稍后重试',
                onOk: async () => {
                    handleClose();
                    setTimeout(() => {
                        const list = document.getElementsByClassName('ant-modal-root')
                        console.log('list', list)
                        const dom = list[0]
                        if (dom) {
                            console.log('dom.remove()', dom.parentNode, dom.remove)
                            dom.remove()
                            if (dom && dom.parentNode) {
                                dom.parentNode.removeChild(dom);
                            }
                        }
                        for (const element of list) {
                            console.log('element', element, element.remove)
                            element && element.remove();
                        }
                    }, 0);
                },
                // onCancel() {
                    // const dom = document.getElementsByClassName('ant-modal-root')[0]
                    // dom && dom.remove();
                // },
            });

            // message.error('当前在线人数较多，小智忙不过来了，请您稍后重试');

            showTip.value = true;
        }
        return data;
    });
};

onMounted(() => {
    // visible.value = true

    // 加载
    // const iframe = document.getElementById('dialog');
    // // iframe.allow = allow.value;
    // iframe.height = modelheight.value;
    // iframe.width = dialogWidth.value;
    // iframe.onload = () => {
    //     // if (!isReady.value) {
    //     //     visible.value = false;
    //     // }
    //     // setTimeout(() => {
    //     //     isReady.value = true;
    //     // }, 100);
    //     iframe.contentWindow.postMessage('load', '*');
    // }
    window.addEventListener('message', onMessage, false);

    // 退出时关闭会话
    window.onbeforeunload = () => {
        if (getToken()) {
            closeSession()
        } 
    }  
});

onUnmounted(() => {
    window.removeEventListener('message', onMessage, false);
});

const onMessage = (e) => {
    if (e.data === 'close' || e.data === 'talk_close') {
        console.log('消息', e);
        visible.value = false;
    } else if (e.data === 'load_ready') {
        if (isReady.value) {
        // iframe.contentWindow.postMessage('load', '*');
        }
    } else if (e.data === 'send_msg') {
        handleOpenSession(true);
    }

};

const onBallClick = async () => {
    if (!isDrag.value) {
        const res = await handleOpenSession();

        if (res) {
            visible.value = true;
            isReady.value = true;
            
            const iframe = document.getElementById('dialog');
            iframe.contentWindow.postMessage('open', '*');

            // iframe.allow = allow.value;
            // iframe.src = dialogUrl.value;
            iframe.height = modelheight.value;
            iframe.width = dialogWidth.value;
        }

    }
};

const handleClose = () => {
    visible.value = false;
    const iframe = document.getElementById('dialog');
    iframe!.contentWindow.postMessage('close-self', '*');
};

const onDragStart = () => {
    isKeydown.value = true;
};

const onDragMove = () => {
    if (isKeydown.value) {
        isDrag.value = true;
    }
};

const onDragEnd = () => {
    setTimeout(() => {
        isKeydown.value = false;
        isDrag.value = false;
    }, 10);
};

const handlePosition = (left) => {
    isLeft.value = left < document.body.clientWidth / 2;
};

function onBeforeLeave() {
  collectPrevLog();
}

function onBeforeEnter() {
  collectLog(permissionId);
}

</script>

<style lang="less" >

.popoverLeft {
    background: transparent !important;
    border: 0px;
    padding: 0px;
    top: unset !important;
    left: 0px !important;
    bottom: -4px !important;
    z-index: 999 !important;
}

.popoverRight {
    background: transparent !important;
    border: 0px;
    padding: 0px;
    top: unset !important;
    left: unset !important;
    right: -2px !important;
    bottom: -4px !important;
    z-index: 999 !important;
}

.ballWrap {
    cursor: pointer;
    display: inline-block;

    .ball {
        background-clip: content-box;
        mix-blend-mode: plus-lighter;
        // position: fixed;
        // left: 300px;
    }
}

.dialogWrap {
    border: 0px;
    border-radius: 24px;
}

.aiBox {
    display: flex;
    align-items: end;
    position: relative;
    height: 100%;
    margin: 16px;
}

</style>
