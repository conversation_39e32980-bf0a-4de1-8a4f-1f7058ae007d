// 单点登录核心类
import { getToken } from '/@/utils/auth';
import { getUrlParam } from '/@/utils';
import { useGlobSetting } from '/@/hooks/setting';
import { validateCasLogin } from '/@/api/sys/user';
import { useUserStore } from '/@/store/modules/user';
import { useSso as useSsoUtil } from '/@/hooks/web/useSso';
import { getProps } from '@/wujie/state';

import WujieVue3 from 'wujie-vue3';
const { bus } = WujieVue3;

const globSetting = useGlobSetting();
const openSso = globSetting.openSso;

export function useSso() {
  const locationUrl = location.protocol + '//' + window.location.host + import.meta.env.VITE_PUBLIC_PATH || '/';
  /**
   * 单点登录
   */
  async function ssoLogin() {
    console.log('per-login');
    if (openSso == 'true') {
      const token = getToken();
      const ticket = getUrlParam('ticket');
      if (!token) {
        if (ticket) {
          await validateCasLogin({
            ticket: ticket,
            service: locationUrl,
          })
            .then((res) => {
              const userStore = useUserStore();
              userStore.setToken(res.token);

              // 发消息
              const props = getProps();
              console.log('props', props);
              bus.$emit('sso-login', props);

              return userStore.afterLoginAction(true, {});
            })
            .catch((res) => {
              useSsoUtil().ssoLoginOut();
            });
        } else {
          // window.location.href = globSetting.casBaseUrl + 'login';
          window.location.href = globSetting.casBaseUrl + '/login?service=' + encodeURIComponent(locationUrl);
        }
      }
    }
  }

  /**
   * 退出登录
   */
  async function ssoLoginOut() {
    console.log('per-logout');
    window.location.href = globSetting.casBaseUrl + '/logout?service=' + encodeURIComponent(locationUrl);
  }
  return { ssoLogin, ssoLoginOut };
}
