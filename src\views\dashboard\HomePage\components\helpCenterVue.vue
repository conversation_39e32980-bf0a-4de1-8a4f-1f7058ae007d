<template>
  <div class="scheduleBox">
    <div class="smallTit">
      <span style="font-weight: bold"> 帮助中心 </span>
      <div class="more" @click="handleMore">更多 <right-outlined /></div>
    </div>
    <div class="helpContentBox">
      <div class="helpContentList" v-for="(item, index) in helpNowList.list" :key="index" @click="handleHelp(item)">《 {{ item.fileName }} 》</div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { RightOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { getKnowledge } from '../index.api';
  const router = useRouter();
  const helpNowList = reactive({
    list: [
      //   {
      //     id: 1,
      //     title: '《电解工序数字化用户使用手册 User Manua》',
      //   },
      //   {
      //     id: 2,
      //     title: '《健康槽管理用户使用手册 User Manua》',
      //   },
      //   {
      //     id: 3,
      //     title: '《质检用户使用手册 User Manua》',
      //   },
      //   {
      //     id: 4,
      //     title: '《抬包全流程用户使用手册 User Manua》',
      //   },
    ],
  });
  //   帮助列表
  function getHelpList() {
    getKnowledge({
      parentId: 2,
      pageNo: 1,
      pageSize: 10,
      fileName: '**',
      column: 'createTime',
      order: 'desc',
    }).then((res) => {
      helpNowList.list = res.records;
    });
  }
  onMounted(() => {
    getHelpList();
  });
  //   帮助中心的跳转
  function handleHelp(item) {
    router.push('/helpCenter');
  }
  //   点击我的日常跳转更多
  function handleMore() {
    router.push('/helpCenter');
  }
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 500px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px 20px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
      .more {
        float: right;
        font-size: 16px;
        color: #717376;
        cursor: pointer;
      }
    }
    .helpContentBox {
      width: 100%;
      height: auto;
      .helpContentList {
        width: 100%;
        height: 41px;
        font-size: 18px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: #000;
        cursor: pointer;
        line-height: 38px;
      }
    }
  }
</style>
