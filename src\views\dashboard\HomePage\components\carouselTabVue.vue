<template>
  <div class="scheduleBox">
    <div class="smallTit" style="font-weight: bold"> 智汇视界 </div>
    <div class="smallTitTab">
      <div class="divList">
        <span :class="navNewIndex == index ? 'active' : ''" v-for="(item, index) in navNewlistTit" :key="index" @click="clickTitle(index)">
          {{ item.title }}
        </span>
      </div>
    </div>
    <div class="allViewBox" v-if="navNewIndex == 0">
      <div class="list" v-for="(item, index) in tabCarousel" :key="index" @click="bigScreenTo(item)">
        <img :src="item.img" alt="" style="border-radius: 8px" />
      </div>
      <!-- <div class="list"> </div> -->
    </div>
    <div class="myCustom3D" v-if="navNewIndex !== 0">
      <div class="leftBox" @click="prevEl" v-if="navNewIndex !== 0">
        <img :src="leftJt" alt="" />
      </div>
      <div class="carouselBox">
        <swiper
          direction="horizontal"
          :loop="true"
          :autoplay="{ disableOnIntercation: false, autoplay: false, delay: 100000 }"
          slidesPerView="3"
          :spaceBetween="0"
          :centeredSlides="true"
          effect="coverflow"
          :coverflowEffect="{ rotate: 30, stretch: 0, depth: 88, modifier: 2, slideShadows: true }"
          :modules="modules"
          @swiper="onSwiper"
        >
          <!-- 总览幻灯片 -->
          <!-- <template v-if="navNewIndex == 0">
            <swiper-slide v-for="(item, index) in tabCarousel" :key="index" @click="bigScreen(item.url)">
              <img :src="item.img" alt="" style="border-radius: 8px" />
            </swiper-slide>
          </template> -->
          <template v-if="navNewIndex == 1">
            <swiper-slide v-for="(item, index) in tabCarousel1" :key="index" @click="bigScreenTo(item)">
              <img :src="item.img" alt="" style="border-radius: 8px" />
            </swiper-slide>
          </template>
          <template v-if="navNewIndex == 2">
            <swiper-slide v-for="(item, index) in tabCarousel2" :key="index" @click="bigScreenTo(item)">
              <img :src="item.img" alt="" style="border-radius: 8px" />
            </swiper-slide>
          </template>
        </swiper>
      </div>
      <div class="rightBox" @click="nextEl" v-if="navNewIndex !== 0">
        <img :src="rightJt" alt="" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { message } from 'ant-design-vue';
  import { ref, reactive } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import leftJt from '/@/assets/images/homeImg/leftJt.png';
  import rightJt from '/@/assets/images/homeImg/rightJt.png';
  import cjk01 from '/@/assets/images/homeImg/cjk01.png';
  import cjk02 from '/@/assets/images/homeImg/cjk02.png';
  import cjk03 from '/@/assets/images/homeImg/cjk03.png';
  import gxh01 from '/@/assets/images/homeImg/gxh01.png';
  import gxh02 from '/@/assets/images/homeImg/gxh02.png';

  import Mask_group from '/@/assets/images/homeSwiper/Mask_group.png';
  import Mask_group_1 from '/@/assets/images/homeSwiper/Mask_group-1.png';
  import Mask_group_2 from '/@/assets/images/homeSwiper/Mask_group-2.png';
  import Mask_group_3 from '/@/assets/images/homeSwiper/Mask_group-3.png';
  import Mask_group_7 from '/@/assets/images/homeSwiper/Mask_group-7.png';
  import Mask_group_5 from '/@/assets/images/homeSwiper/Mask_group-5.png';
  import Mask_group_6 from '/@/assets/images/homeSwiper/Mask_group-6.png';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Autoplay, Navigation, EffectCoverflow } from 'swiper/modules';
  import { usePermissionStore } from '/@/store/modules/permission';
  import WujieVue from 'wujie-vue3';
  const { bus } = WujieVue;
  const permissionStore = usePermissionStore();
  import 'swiper/css';
  import 'swiper/css/navigation';
  const router = useRouter();
  const modules = reactive([Autoplay, Navigation, EffectCoverflow]);
  let useSwiper = null;
  // 初始化 swiper
  const onSwiper = (swiper) => {
    useSwiper = swiper;
  };
  // 通过实例方法自定义上一个下一个事件
  const prevEl = () => {
    useSwiper.slidePrev();
  };
  const nextEl = () => {
    useSwiper.slideNext();
  };
  const navNewlistTit = ref([
    {
      title: '总览',
      id: 1,
    },
    {
      title: '槽健康',
      id: 2,
    },
    {
      title: '工序数字化',
      id: 3,
    },
  ]);
  const tabCarousel = ref([
    {
      id: 1,
      img: cjk01,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 2,
      img: cjk02,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 3,
      img: cjk03,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 4,
      img: gxh01,
      url: '/middle/pdos-web?pdos_web=/work-area-large-screen',
      path: '/work-area-large-screen',
      middleCode: '/middle/pdos-web',
      seaechCode: 'pdos_web',
    },
    {
      id: 5,
      img: gxh02,
      url: '/middle/pdos-web?pdos_web=/work-area-admin-large-screen',
      path: '/work-area-admin-large-screen',
      middleCode: '/middle/pdos-web',
      seaechCode: 'pdos_web',
    },
    {
      id: 6,
      img: Mask_group,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard',
      path: '/screen/dashboard',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },

    {
      id: 7,
      img: Mask_group_1,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard/qc',
      path: '/screen/dashboard/qc',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },
    {
      id: 8,
      img: Mask_group_2,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard/bgos',
      path: '/screen/dashboard/bgos',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },

    {
      id: 9,
      img: Mask_group_3,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard/electrolytic-cell',
      path: '/screen/dashboard/electrolytic-cell',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },
    {
      id: 10,
      img: Mask_group_7,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard/unusualpot',
      path: '/screen/dashboard/unusualpot',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },
    {
      id: 11,
      img: Mask_group_5,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard/purify',
      path: '/screen/dashboard/purify',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },
    {
      id: 12,
      img: Mask_group_6,
      url: '/middle/global-web?global_web=/global-web/screen/dashboard/anode',
      path: '/screen/dashboard/anode',
      middleCode: '/middle/global-web',
      seaechCode: 'global_web',
    },
  ]);
  const tabCarousel1 = ref([
    {
      id: 1,
      img: cjk01,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 2,
      img: cjk02,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 3,
      img: cjk03,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 4,
      img: cjk01,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 5,
      img: cjk02,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
    {
      id: 6,
      img: cjk03,
      url: '/middle/slot-health-manage-web?slot_health_manage_web=/healthManagement/bulletinBoard',
      path: '/healthManagement/bulletinBoard',
      middleCode: '/middle/slot-health-manage-web',
      seaechCode: 'slot_health_manage_web',
    },
  ]);
  const tabCarousel2 = ref([
    {
      id: 4,
      img: gxh01,
      url: '/middle/pdos-web?pdos_web=/work-area-large-screen',
      path: '/work-area-large-screen',
      middleCode: '/middle/pdos-web',
      seaechCode: 'pdos_web',
    },
    {
      id: 5,
      img: gxh02,
      url: '/middle/pdos-web?pdos_web=/work-area-admin-large-screen',
      path: '/work-area-admin-large-screen',
      middleCode: '/middle/pdos-web',
      seaechCode: 'pdos_web',
    },
    {
      id: 6,
      img: gxh01,
      url: '/middle/pdos-web?pdos_web=/work-area-large-screen',
      path: '/work-area-large-screen',
      middleCode: '/middle/pdos-web',
      seaechCode: 'pdos_web',
    },
    {
      id: 7,
      img: gxh02,
      url: '/middle/pdos-web?pdos_web=/work-area-admin-large-screen',
      path: '/work-area-admin-large-screen',
      middleCode: '/middle/pdos-web',
      seaechCode: 'pdos_web',
    },
  ]);
  const navNewIndex = ref(0);
  function bigScreenTo(item) {
    let isShowRouter;
    let routerList = permissionStore.$state.backMenuList;
    isShowRouter = routerList.some((o) => o.path == item.middleCode);
    if (isShowRouter) {
      if (item.seaechCode == 'global_web') {
        bus.$emit('routeChange', item.path);
      } else {
        bus.$emit(`${item.seaechCode}-routeChange`, item.path);
      }
      router.push(item.url);
    } else {
      message.warning('暂无权限访问，可联系管理员配置');
    }
  }
  function clickTitle(val) {
    navNewIndex.value = val;
  }
</script>
<style scoped lang="less">
  .swiper-slide {
    width: 100%;
    height: auto;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
  }
  .swiper-slide-prev {
    transform: scale(0.8);
  }
  .swiper-slide-next {
    transform: scale(0.8);
  }
  .scheduleBox {
    width: 100%;
    // height: 568px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
    }
    .smallTitTab {
      font-size: 22px;
      height: 60px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
      //   display: flex;
      //   justify-contednt: space-between;
      //   align-items: center;
      border-bottom: 1px solid #ecedf0;
      .divList {
        font-size: 22px;
        span {
          display: inline-block;
          height: 59px;
          line-height: 59px;
          color: #000;
          border-bottom: 2px solid transparent;
          margin-right: 20px;
          cursor: pointer;
        }
        .active {
          font-weight: bold;
          color: #1677ff;
          border-bottom: 2px solid #1677ff;
        }
      }
    }
    .myCustom3D {
      width: 100%;
      //   height: 400px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .leftBox,
      .rightBox {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        cursor: pointer;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .carouselBox {
        width: calc(100% - 140px);
        // height: 340px;
        overflow: hidden;
        .carouselListBox {
          width: 100%;
          //   height: 340px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          position: relative;
          .carList {
            width: 604px;
            height: 340px;
            border-radius: 8px;
            margin-right: 20px;
            // position: absolute;
            // top: 0;
            // margin-left: calc(50% - 302px);
            background: #1677ff;
            img {
              width: 100%;
              height: 100%;
              border-radius: 8px;
            }
          }
          .scale50 {
            width: 517px;
            height: 291px;
            border-radius: 8px;
            margin-right: 20px;
            // transform: scale(0.75, 0.75);
            background: #1677ff;
            img {
              width: 100%;
              height: 100%;
              border-radius: 8px;
            }
          }
        }
      }
    }
    .allViewBox {
      width: 100%;
      // height: 240px;
      // background: #f0f;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap; /* 允许换行，如果需要的话 */
      .list {
        // width: 20%;
        // margin-right: 10px;
        text-align: center;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        flex: 0 0 calc(16.666% - 10px);
        // margin: 5px;
        margin-top: 16px;
        // height: 318px;
        &:last-child {
          margin-right: 0;
          flex: 0 16%;
        }
        // img {
        //   width: 362px;
        //   height: 204px;
        // }
      }
    }
  }
</style>
