import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { h } from 'vue';

function stripHTMLTags(html) {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/ +/g, ' ')
    .trim(); // 使用正则表达式去除 HTML 标签
}

export const columns: BasicColumn[] = [
  {
    title: '标题',
    width: 150,
    dataIndex: 'titile',
  },
  {
    title: '内容',
    width: 250,
    dataIndex: 'msgContent',
    customRender: ({ text }) => {
      return stripHTMLTags(text);
    },
  },
  {
    title: '消息类型',
    dataIndex: 'msgCategory',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'msg_category');
    },
  },
  {
    title: '发布时间',
    width: 100,
    dataIndex: 'sendTime',
  },
  {
    title: '发布人',
    width: 100,
    dataIndex: 'senderName',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'sendTime',
    label: '发布日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择发布日期',
    },
    colProps: { span: 5 },
  },
  {
    field: 'titile',
    label: '标题',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'msgCategory',
    label: '消息类型',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'msg_category',
      placeholder: '请选择',
    },
    colProps: { span: 5 },
  },
];

export const detailNotice: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },

  {
    field: 'titile',
    label: '通知标题',
    component: 'Input',

    componentProps: {
      placeholder: '请输入标题',
    },
  },

  {
    field: 'sendTime',
    label: '发布时间',
    component: 'Input',
  },

  {
    field: 'senderName',
    label: '发布人',
    component: 'Input',
  },

  {
    field: 'msgContent',
    label: '通知内容',
    component: 'InputTextArea',
    colProps: { span: 24 },
    // componentProps: {
    //   // placeholder: '请输入通知内容',
    //   autoSize: { minRows: 15, maxRows: 50 }, // 或者简单使用 autoSize: true 来自动调整大小
    //   // autoSize: true
    // },
    render: ({ model, field }) => {
      //渲染自定义组件，以Input为例
      return h('div', {
        innerHTML: model['msgContent'],
        style: { color: '#000000a6', width: '100%', background: '#f5f5f5', padding: '5px', border: '1px solid #d9d9d9', borderRadius: '3px' },
      });
    },
  },
];
