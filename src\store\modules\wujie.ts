import { defineStore } from 'pinia';
import { store } from '/@/store';

interface AppState {
  // wujie子应用保活列表
  alives: any;
}
export const useWujieStore = defineStore({
  id: 'wujie',
  state: (): AppState => ({
    alives: {},
  }),
  getters: {
    getAlives(): any {
      return this.alives;
    },
  },
  actions: {
    setAlives(value: {}): void {
      this.alives = {
        ...this.alives,
        ...value
      };
    },
  },
});

// Need to be used outside the setup
export function useWujieStoreWithOut() {
  return useWujieStore(store);
}
