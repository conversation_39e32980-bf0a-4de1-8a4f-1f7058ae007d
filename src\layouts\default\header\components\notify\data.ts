export interface ListItem {
  id: string;
  avatar: string;
  // 通知的标题内容
  title: string;
  // 是否在标题上显示删除线
  titleDelete?: boolean;
  datetime: string;
  type: string;
  read?: boolean;
  description: string;
  clickClose?: boolean;
  extra?: string;
  color?: string;
  // 优先级
  priority?: string;
}

export enum PriorityTypes {
  // 低优先级，一般消息
  L = 'L',
  // 中优先级，重要消息
  M = 'M',
  // 高优先级，紧急消息
  H = 'H',
}

export interface TabItem {
  key: string;
  name: string;
  list: ListItem[];
  unreadlist?: ListItem[];
  count: number;
}

export const tabListData: TabItem[] = [
  {
    key: '1',
    name: '通知',
    list: [],
    count: 0,
  },
  {
    key: '2',
    name: '系统消息',
    list: [],
    count: 0,
  },
  // {
  //   key: '3',
  //   name: '待办',
  //   list: [
  //     {
  //       id: '000000009',
  //       avatar: '',
  //       title: '任务名称',
  //       description: '任务需要在 2017-01-12 20:00 前启动',
  //       datetime: '',
  //       extra: '未开始',
  //       color: '',
  //       type: '3',
  //     },
  //     {
  //       id: '000000010',
  //       avatar: '',
  //       title: '第三方紧急代码变更',
  //       description: '冠霖 需在 2017-01-07 前完成代码变更任务',
  //       datetime: '',
  //       extra: '马上到期',
  //       color: 'red',
  //       type: '3',
  //     },
  //     {
  //       id: '000000011',
  //       avatar: '',
  //       title: '信息安全考试',
  //       description: '指派竹尔于 2017-01-09 前完成更新并发布',
  //       datetime: '',
  //       extra: '已耗时 8 天',
  //       color: 'gold',
  //       type: '3',
  //     },
  //     {
  //       id: '000000012',
  //       avatar: '',
  //       title: 'ABCD 版本发布',
  //       description: '指派竹尔于 2017-01-09 前完成更新并发布',
  //       datetime: '',
  //       extra: '进行中',
  //       color: 'blue',
  //       type: '3',
  //     },
  //   ],
  // },
];
