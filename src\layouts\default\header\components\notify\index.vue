<template>
  <div :class="prefixCls">
    <!-- <Badge :count="count" :overflowCount="9" :offset="[-4, 10]" :numberStyle="numberStyle" @click="clickBadge">
      <BellOutlined />
    </Badge> -->
    <Badge @click="clickBadge" :dot="count ? true : false" :offset="[-5, 5]">
      <BellOutlined />
    </Badge>

    <DynamicNotice ref="dynamicNoticeRef" v-bind="dynamicNoticeProps" />
    <DetailModal @register="registerDetail" />

    <sys-message-modal @register="registerMessageModal" @refresh="reloadCount"></sys-message-modal>
  </div>
</template>
<script lang="ts">
  import { computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, unref } from 'vue';
  import { Badge, Popover, Tabs } from 'ant-design-vue';
  import { BellOutlined } from '@ant-design/icons-vue';
  import { tabListData } from './data';
  import { editCementSend, listCementByUser } from './notify.api';
  import NoticeList from './NoticeList.vue';
  import DetailModal from '/@/views/monitor/mynews/DetailModal.vue';
  import DynamicNotice from '/@/views/monitor/mynews/DynamicNotice.vue';
  import { useModal } from '/@/components/Modal';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useUserStore } from '/@/store/modules/user';
  import { connectWebSocket, onWebSocket } from '/@/hooks/web/useWebSocket';
  import { readAllMsg } from '/@/views/monitor/mynews/mynews.api';
  import { getToken } from '/@/utils/auth';
  import md5 from 'crypto-js/md5';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';

  import SysMessageModal from '/@/views/system/message/components/SysMessageModal.vue';
  import { getMessagePage } from '@/api/message/message';
  import WujieVue3 from 'wujie-vue3';

  const { bus } = WujieVue3;

  export default defineComponent({
    components: {
      Popover,
      BellOutlined,
      Tabs,
      TabPane: Tabs.TabPane,
      Badge,
      NoticeList,
      DetailModal,
      DynamicNotice,
      SysMessageModal,
    },
    setup() {
      const { prefixCls } = useDesign('header-notify');
      const instance: any = getCurrentInstance();
      const userStore = useUserStore();
      const glob = useGlobSetting();
      const dynamicNoticeProps = reactive({ path: '', formData: {} });
      const [registerDetail, detailModal] = useModal();
      const listData = ref(tabListData);
      const alarmMsgTotal = ref(0);
      const activeKey = ref('');

      const count = computed(() => {
        let countNum = 0;
        for (let i = 0; i < listData.value.length; i++) {
          let itemCount = isNaN(listData?.value[i]?.count) ? 0 : Number(listData.value[i].count);
          countNum += itemCount;
        }
        return countNum + alarmMsgTotal.value;
      });

      const [registerMessageModal, { openModal: openMessageModal }] = useModal();
      function clickBadge() {
        //消息列表弹窗前去除角标
        for (let i = 0; i < listData.value.length; i++) {
          listData.value[i].count = 0;
        }
        openMessageModal(true, {});
      }

      const popoverVisible = ref<boolean>(false);
      onMounted(() => {
        //  initWebSocket();
        setTimeout((_) => {
          getRouterType(window.location.href);
          loadData();
        });
      });

      function mapAnnouncement(item) {
        return {
          ...item,
          title: item.titile,
          description: item.msgAbstract,
          datetime: item.sendTime,
        };
      }
      const loading = ref(false);

      // 获取系统消息
      async function loadData() {
        try {
          let { anntMsgList, sysMsgList, anntMsgTotal, sysMsgTotal } = await listCementByUser({
            pageSize: 8,
          });
          listData.value[0].list = anntMsgList.map(mapAnnouncement);
          listData.value[1].list = sysMsgList.map(mapAnnouncement);
          listData.value[0].count = anntMsgTotal;
          listData.value[1].count = sysMsgTotal;
        } catch (e) {
          console.warn('系统消息通知异常：', e);
        }
        const param = {
          pageIndex: 1,
          pageSize: 5,
          filer: {
            readFlag: 'N',
          },
        };
        const res = await getMessagePage(param, activeKey.value);
        const total = Number(res.total);
        alarmMsgTotal.value = total;
        loading.value = false;
      }
      const getRouterType = (str: string) => {
        if (str.includes('ipms_ly1_a')) {
          activeKey.value = 'ipms_ly1_a';
        } else if (str.includes('ipms_ly1_c')) {
          activeKey.value = 'ipms_ly1_c';
        } else if (str.includes('ipms_ly5_c')) {
          activeKey.value = 'ipms_ly5_c';
        } else {
          activeKey.value = '';
        }
      };
      listenerRouteChange((route) => {
        if (loading.value) return;
        loading.value = true;
        getRouterType(route.path);
        getToken() && loadData();
      });

      function onNoticeClick(record) {
        try {
          editCementSend(record.id);
          loadData();
        } catch (e) {
          console.error(e);
        }
        if (record.openType === 'component') {
          dynamicNoticeProps.path = record.openPage;
          dynamicNoticeProps.formData = { id: record.busId };
          instance.refs.dynamicNoticeRef?.detail(record.openPage);
        } else {
          detailModal.openModal(true, {
            record,
            isUpdate: true,
          });
        }
        popoverVisible.value = false;
      }

      // 初始化 WebSocket
      function initWebSocket() {
        let token = getToken();
        //将登录token生成一个短的标识
        let wsClientId = md5(token);
        let userId = unref(userStore.getUserInfo).id + '_' + wsClientId;
        // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
        let url = glob.domainUrl?.replace('https://', 'wss://').replace('http://', 'ws://') + '/websocket/' + userId;
        connectWebSocket(url);
        onWebSocket(onWebSocketMessage);
      }

      function onWebSocketMessage(data) {
        if (data.cmd === 'topic' || data.cmd === 'user') {
          //update-begin-author:taoyan date:2022-7-13 for: VUEN-1674【严重bug】系统通知，为什么必须刷新右上角才提示
          //后台保存数据太慢 前端延迟刷新消息
          setTimeout(() => {
            loadData();
          }, 1000);
          //update-end-author:taoyan date:2022-7-13 for: VUEN-1674【严重bug】系统通知，为什么必须刷新右上角才提示
        }
      }

      // 清空消息
      function onEmptyNotify() {
        popoverVisible.value = false;
        readAllMsg({}, loadData);
      }
      async function reloadCount(id) {
        try {
          await editCementSend(id);
          await loadData();
        } catch (e) {
          console.error(e);
        }
      }

      const socketRef = ref<WebSocket | null>(null);
      const timer = ref<NodeJS.Timeout | null>(null);

      listenerRouteChange((route) => {
        const userStore = useUserStore();
        const loginId = userStore.userInfo.id;

        if (socketRef.value) {
          socketRef.value?.close();
          clearInterval(timer?.value);
          socketRef.value = null;
        }

        let path = '';
        if (route.name?.includes('ipms_')) {
          const arr = route.name?.split('_') || [];
          path = `${arr[1]}-${arr[2]}-ipms`;
        } else {
          path = 'ly1-a-ipms';
        }
        let url;
        if (glob.domainUrl.indexOf('http') === 0) {
          url = glob.domainUrl?.replace('https://', 'wss://').replace('http://', 'ws://') + path + '/api/webSocket/' + loginId;
        } else {
          url = (location.protocol + glob.domainUrl).replace('https://', 'wss://').replace('http://', 'ws://') + path + '/api/webSocket/' + loginId;
        }

        // socketRef.value = new WebSocket(`ws://ipms-api.dev.hongqiaocloud.com/ly1-a-ipms/api/webSocket/${loginId}`);
        console.log('web:', url);
        socketRef.value = new WebSocket(url);

        const socket = socketRef.value;

        socket?.addEventListener('open', () => {
          console.log('socket onopen');
          // getWebsocketSend({ sid: loginId, msg: '测试消息' }).then((res: any) => {
          //   console.log('大师兄回来了');
          // });
          timer.value = setInterval(() => {
            socket?.send('测试消息');
          }, 30000);
        });

        socket?.addEventListener('message', (e) => {
          if (import.meta.env.MODE !== 'development') {
            console.log('socket onmessage', e);
          }
          if (e.data != '' && e.data != 'conn_success' && e.data != '测试消息') {
            const isObj = e.data.includes('{');
            const data = isObj ? JSON.parse(e.data) : {};
            // 发消息
            if (data.code === 'safe') {
              bus.$emit('msg-safe', data);
            } else if (data.code === 'monitor') {
              bus.$emit('msg-monitor', data);
            }
            // setNewMessage(e.data);
            // setIsOpen(true);
            // setTimeout(() => {
            //   setIsOpen(false);
            // }, 6000);
          }
        });

        socket?.addEventListener('close', () => {
          console.log('socket onclose');
        });
      });

      return {
        prefixCls,
        listData,
        count,
        clickBadge,
        registerMessageModal,
        reloadCount,
        onNoticeClick,
        onEmptyNotify,
        numberStyle: {},
        popoverVisible,
        registerDetail,
        dynamicNoticeProps,
      };
    },
  });
</script>
<style lang="less">
  //noinspection LessUnresolvedVariable
  @prefix-cls: ~'@{namespace}-header-notify';

  .@{prefix-cls} {
    padding-top: 2px;

    &__overlay {
      max-width: 340px;

      .ant-popover-inner-content {
        padding: 0;
      }

      .ant-tabs-nav {
        margin-bottom: 12px;
      }

      .ant-list-item {
        padding: 12px 24px;
        transition: background-color 300ms;
      }

      .bottom-buttons {
        text-align: center;
        border-top: 1px solid #f0f0f0;
        height: 42px;

        .ant-btn {
          border: 0;
          height: 100%;

          &:first-child {
            border-right: 1px solid #f0f0f0;
          }
        }
      }
    }

    .ant-tabs-content {
      width: 300px;
    }

    .ant-badge {
      font-size: 18px;

      .ant-badge-count {
        @badget-size: 16px;
        width: @badget-size;
        height: @badget-size;
        min-width: @badget-size;
        line-height: @badget-size;
        padding: 0;

        .ant-scroll-number-only > p.ant-scroll-number-only-unit {
          font-size: 14px;
          height: @badget-size;
        }
      }

      .ant-badge-multiple-words {
        padding: 0 0 0 2px;
        font-size: 12px;
      }

      svg {
        width: 0.9em;
      }
    }
  }

  // 兼容黑暗模式
  [data-theme='dark'] .@{prefix-cls} {
    &__overlay {
      .ant-list-item {
        &:hover {
          background-color: #111b26;
        }
      }

      .bottom-buttons {
        border-top: 1px solid #303030;

        .ant-btn {
          &:first-child {
            border-right: 1px solid #303030;
          }
        }
      }
    }
  }
</style>
