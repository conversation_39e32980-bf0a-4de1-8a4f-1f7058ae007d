<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="900px" :bodyStyle="bodyStyle" destroyOnClose>
    <a-tabs v-model:activeKey="activeKey" @change="handleChangeTabs" size="small">
      <a-tab-pane :forceRender="true" v-for="tabItem in tabs" :tab="tabItem.modalTab" :key="tabItem.key" />
    </a-tabs>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { scheduleFormSchema, logFormSchema, tabsType } from './schedule.data';
  import { update } from './schedule.api';

  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const tabs = ref([...tabsType]);
  const activeKey = ref('schedule');
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, resetSchema }] = useForm({
    schemas: scheduleFormSchema,
    showActionButtonGroup: false,
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    activeKey.value = data.activeKey || 'schedule';
    handleChangeTabs(activeKey.value);
  });
  const bodyStyle = {
    height: '400px',
  };
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
  const handleChangeTabs = (value) => {
    if (value === unref(tabs)[0].key) {
      resetSchema(scheduleFormSchema);
      resetFields();
    } else if (value === unref(tabs)[1].key) {
      resetSchema(logFormSchema);
      resetFields();
    }
  };

  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      setModalProps({ confirmLoading: true });
      //提交表单
      let isWorkLog = unref(activeKey) === unref(tabs)[1].key;
      const { dateTime, ...otherValues } = values;
      let params = {
        ...otherValues,
      };
      if (!isWorkLog) {
        params = {
          ...params,
          startTime: dateTime.split(',')[0],
          endTime: dateTime.split(',')[1],
        };
      }
      //update-end-author:liusq---date:20230404--for: [issue#429]新增通知公告提交指定用户参数有undefined ---
      await update(params, isWorkLog);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style scoped></style>
