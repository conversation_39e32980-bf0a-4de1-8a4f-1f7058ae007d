<script setup lang="ts">
  import { message, Tooltip } from 'ant-design-vue';
  import { HourglassOutlined, SlackOutlined } from '@ant-design/icons-vue';
  import { usePermissionStore } from '/@/store/modules/permission';

  // import { getToken } from '/@/utils/auth';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { computed } from 'vue';

  const permissionStore = usePermissionStore();
  const userStore = useUserStore();

  const router = useRouter();

  function handleClick(router, name) {
    navRoute(router, name);
    // const origin = document.location.origin;
    // const tokenTail = '?loginToken=' + getToken();
    // if (['192.', '127.'].some((element) => origin.includes(element))) {
    //   window.open('//' + document.location.hostname + ':3110/tokenLogin' + tokenTail, '_blank');
    // } else {
    //   window.open(origin + '/global-web/tokenLogin' + tokenTail, '_blank');
    // }
  }

  function navRoute(item, name) {
    let isShowRouter;
    let routerList = permissionStore.$state.backMenuList;
    isShowRouter = routerList.some((o) => o.path == item);
    if (isShowRouter) {
      // location.href = document.location.origin + '/middle/global-web';
      if (router.currentRoute.value.path === item) {
        Object.keys(window).forEach((key) => {
          if (window[key] && window[key].name && window[key].name == name) {
            window[key].$wujie.bus.$emit('routeChange', '/screen/dashboard');
          }
        });
      } else {
        router.push(item);
      }
      // router.push({
      //   path: item,
      //   params: {
      //     global_web: '/screen/dashboard',
      //   },
      // });
    } else {
      // console.log('得到');
      message.warning('暂无权限访问，可联系管理员配置');
    }
  }

  const isShowGlobalWebMenu = computed(() => {
    return userStore.getCorpId === '1738003195015249921' && userStore.hasAppPermission;
  });
</script>

<template>
  <!--  <Tooltip title="电解槽在线测温" placement="bottom" :mouseEnterDelay="0.5" @click="handleClick('/middle/temp-monitor-web', 'temp-monitor-web')">-->
  <!--    <hourglass-outlined />-->
  <!--  </Tooltip>-->

  <Tooltip
    v-if="isShowGlobalWebMenu"
    title="电解铝概览"
    placement="bottom"
    :mouseEnterDelay="0.5"
    @click="handleClick('/middle/global-web', 'global_web')"
  >
    <SlackOutlined />
  </Tooltip>
</template>

<style scoped lang="less"></style>
