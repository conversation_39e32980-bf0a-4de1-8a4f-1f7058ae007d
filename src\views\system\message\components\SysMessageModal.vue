<template>
  <BasicModal
    :canFullscreen="false"
    :draggable="false"
    :closable="false"
    @register="registerModal"
    wrapClassName="sys-msg-modal"
    :width="800"
    :footer="null"
    destroyOnClose
  >
    <template #title>
      <div class="sys-msg-modal-title">
        <div class="close-icon-box">
          <close-outlined @click="closeModal" />
        </div>
        <div class="title"></div>
        <div class="ant-tabs-nav-wrap">
          <div class="ant-tabs-nav-scroll">
            <div class="ant-tabs-nav ant-tabs-nav-animated">
              <div>
                <!--        产品化 告警消息        -->
                <div
                  v-show="showTempWarningMsg"
                  aria-disabled="false"
                  aria-selected="false"
                  class="ant-tabs-tab"
                  :style="{ color: activeKey == 'tempWarn' ? '#1677FF' : '' }"
                  @click="(e) => handleChangeTab(e, 'tempWarn')"
                  role="tab"
                >
                  告警消息
                </div>
                <div
                  v-show="showWarningMsg"
                  aria-disabled="false"
                  aria-selected="false"
                  class="ant-tabs-tab"
                  :style="{ color: activeKey == 'warn' ? '#1677FF' : '' }"
                  @click="(e) => handleChangeTab(e, 'warn')"
                  role="tab"
                >
                  告警消息
                </div>
                <div
                  @click="(e) => handleChangeTab(e, 'sys')"
                  role="tab"
                  aria-disabled="false"
                  aria-selected="true"
                  class="ant-tabs-tab"
                  :style="{ color: activeKey == 'sys' ? '#1677FF' : '' }"
                >
                  系统消息
                </div>
              </div>
              <div
                class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
                :style="{
                  transform: activeKey == 'sys' ? 'translate3d(0px, 0px, 0px)' : 'translate3d(120px, 0px, 0px)',
                  display: 'block',
                  width: '88px',
                }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 头部图标 -->
        <div class="icon-right">
          <div class="icons">
            <a-popover placement="bottomRight" :overlayStyle="{ width: '400px' }" trigger="click" v-model:visible="showSearch">
              <template #content>
                <div>
                  <span class="search-label">回复、提到我的人?：</span>
                  <span style="display: inline-block">
                    <div v-if="searchParams.fromUser" class="selected-user">
                      <span>{{ searchParams.realname }}</span>
                      <span class="clear-user-icon"><close-outlined style="font-size: 12px" @click="clearSearchParamsUser" /></span>
                    </div>
                    <a-button v-else type="dashed" shape="circle" @click="openSelectPerson">
                      <plus-outlined />
                    </a-button>
                  </span>
                </div>
                <div class="search-date">
                  <div class="date-label">时间：</div>
                  <div class="date-tags">
                    <div class="tags-container">
                      <div v-for="item in dateTags" :class="item.active == true ? 'tag active' : 'tag'" @click="handleClickDateTag(item)"
                        >{{ item.text }}
                      </div>
                    </div>
                    <div class="cust-range-date" v-if="showRangeDate">
                      <a-range-picker v-model:value="searchRangeDate" @change="handleChangeSearchDate" />
                    </div>
                  </div>
                </div>
              </template>

              <span v-if="conditionStr" class="anticon filtera">
                <filter-outlined />
                <span style="font-size: 12px; margin-left: 3px">{{ conditionStr }}</span>
                <span style="display: flex; margin: 0 5px"><close-outlined style="font-size: 12px" @click="clearAll" /></span>
              </span>
              <filter-outlined v-else />
            </a-popover>
            <close-outlined @click="closeModal" />
          </div>
        </div>
      </div>
    </template>

    <div class="sys-message-card">
      <a-tabs :activeKey="activeKey" center @tab-click="handleChangePanel">
        <template #renderTabBar>
          <div></div>
        </template>

        <!--    产品化告警列表    -->
        <a-tab-pane v-if="showTempWarningMsg" tab="告警消息" key="tempWarn">
          <tempWarningMsg />
        </a-tab-pane>

        <!-- 标星 -->
        <a-tab-pane v-if="showWarningMsg" tab="告警消息" key="warn" forceRender>
          <div class="tag-wrap">
            <div>
              <CheckableTag
                v-for="item in messageList"
                class="tag-box"
                :style="
                  tagIdCheck?.indexOf(item?.eventClassName) > -1
                    ? {
                        color: '#1677FF',
                        background: '#E6F4FF',
                      }
                    : {
                        color: '#86909C',
                        background: '#F2F3F5',
                      }
                "
                :key="item?.eventClassName"
                :checked="tagIdCheck?.indexOf(item?.eventClassName) > -1"
                @click="
                  (isCheck) => {
                    tagIdCheck = item.eventClassName;
                    // handleTagChange(isCheck, item);
                  }
                "
              >
                <span :style="item?.total != 0 ? {} : { display: 'none' }">
                  <span :style="tagIdCheck?.indexOf(item?.eventClassName) > -1 ? { color: '#1677FF' } : { color: '#86909C' }">
                    {{ item?.eventClassName + '(' + item?.total + ')' }}
                  </span>
                </span>

                <span :style="item?.total == 0 ? {} : { display: 'none' }">
                  <span :style="tagIdCheck?.indexOf(item?.eventClassName) > -1 ? { color: '#1677FF' } : { color: '#86909C' }">
                    {{ item?.eventClassName }}
                  </span>
                </span>
              </CheckableTag>
            </div>
            <a-button type="primary" @click="readButtonClick">一键已读</a-button>
          </div>
          <sys-message-list ref="warnMessageRef" star @close="hrefThenClose" @detail="showDetailModal" />
        </a-tab-pane>

        <a-tab-pane tab="系统消息" key="sys" forceRender>
          <sys-message-list ref="sysMessageRef" @close="hrefThenClose" @detail="showDetailModal" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicModal>

  <user-select-modal
    isRadioSelection
    :showButton="false"
    labelKey="realname"
    rowKey="username"
    @register="regModal"
    @get-select-result="getSelectedUser"
  />

  <DetailModal @register="registerDetail" />
</template>

<script>
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { FilterOutlined, CloseOutlined, BellFilled, ExclamationOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { JSelectUser } from '/@/components/Form';
  import { ref, unref, reactive, computed, watch, onMounted } from 'vue';
  import SysMessageList from './SysMessageList.vue';
  import tempWarningMsg from './tempWarningMsg.vue';
  import UserSelectModal from '/@/components/Form/src/jeecg/components/modal/UserSelectModal.vue';
  import DetailModal from '/@/views/monitor/mynews/DetailModal.vue';
  import { getMessageGroup, getMessagePage, updateReadFlag } from '@/api/message/message';
  import { Tag, message } from 'ant-design-vue';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';
  import { getDictItemsByCode } from '@/utils/dict';

  export default {
    name: 'SysMessageModal',
    components: {
      BasicModal,
      FilterOutlined,
      CloseOutlined,
      BellFilled,
      ExclamationOutlined,
      JSelectUser,
      SysMessageList,
      UserSelectModal,
      PlusOutlined,
      DetailModal,
      CheckableTag: Tag.CheckableTag,
      tempWarningMsg,
    },
    emits: ['register', 'refresh'],
    setup(_p, { emit }) {
      const sysMessageRef = ref();
      const warnMessageRef = ref();
      const activeKey = ref('warn');
      const routerType = ref('');

      function handleChangeTab(e, key) {
        activeKey.value = key;
        loadData();
      }

      function handleChangePanel(key) {
        activeKey.value = key;
      }

      onMounted(() => {
        //  initWebSocket();
        setTimeout((_) => {
          getRouterType(window.location.href);
        });
      });

      // 查询区域存储值
      const searchParams = reactive({
        fromUser: '',
        realname: '',
        rangeDateKey: '',
        rangeDate: [],
      });

      function loadData() {
        if (activeKey.value == 'sys') {
          let params = {
            fromUser: searchParams.fromUser,
            rangeDateKey: searchParams.rangeDateKey,
            rangeDate: searchParams.rangeDate,
          };
          sysMessageRef.value.reload(params, activeKey.value, routerType.value);
        } else {
          queryMessageGroup();
        }
      }

      //useModalInner
      const [registerModal, { closeModal }] = useModalInner(async () => {
        //每次弹窗打开 加载最新的数据
        loadData();
      });

      const showSearch = ref(false);

      function handleChangeSearchPerson(value, a) {
        // console.log('选择改变', value, a);
        showSearch.value = true;
      }

      const dateTags = reactive([
        { key: 'jt', text: '今天', active: false },
        { key: 'zt', text: '昨天', active: false },
        { key: 'qt', text: '前天', active: false },
        { key: 'bz', text: '本周', active: false },
        { key: 'sz', text: '上周', active: false },
        { key: 'by', text: '本月', active: false },
        { key: 'sy', text: '上月', active: false },
        { key: 'zdy', text: '自定义', active: false },
      ]);

      function handleClickDateTag(item) {
        for (let a of dateTags) {
          if (a.key != item.key) {
            a.active = false;
          }
        }
        item.active = !item.active;
        if (item.active == false) {
          searchParams.rangeDateKey = '';
        } else {
          searchParams.rangeDateKey = item.key;
        }
        if (item.key == 'zdy') {
          // 自定义日期查询走的是 handleChangeSearchDate
          if (item.active == false) {
            searchParams.rangeDate = [];
            loadData();
          }
        } else {
          loadData();
        }
      }

      const showRangeDate = computed(() => {
        let temp = dateTags.filter((i) => i.active == true);
        if (temp && temp.length > 0) {
          if (temp[0].text == '自定义') {
            return true;
          }
        }
        return false;
      });
      const searchRangeDate = ref([]);

      function handleChangeSearchDate(_value, dateStringArray) {
        searchParams.rangeDate = [...dateStringArray];
        loadData();
      }

      function hrefThenClose(id, close) {
        emit('refresh', id);
        close && closeModal();
      }

      // 有查询条件值的时候显示该字符串
      const conditionStr = computed(() => {
        const { fromUser, rangeDateKey, realname } = searchParams;
        if (!fromUser && !rangeDateKey) {
          return '';
        }
        let arr = [];
        if (fromUser) {
          arr.push(realname);
        }
        if (rangeDateKey) {
          let rangDates = dateTags.filter((item) => item.key == rangeDateKey);
          if (rangDates && rangDates.length > 0) {
            arr.push(rangDates[0].text);
          }
        }
        return arr.join('、');
      });

      //注册model
      const [regModal, { openModal }] = useModal();

      function getSelectedUser(options, value) {
        if (options && options.length > 0) {
          searchParams.fromUser = value;
          searchParams.realname = options[0].label;
        }
      }

      function openSelectPerson() {
        openModal(true, {});
      }

      function clearSearchParamsUser() {
        searchParams.fromUser = '';
        searchParams.realname = '';
      }

      function clearAll() {
        searchParams.fromUser = '';
        searchParams.realname = '';
        searchParams.rangeDateKey = '';
        searchParams.rangeDate = [];
        for (let a of dateTags) {
          a.active = false;
        }
      }

      const [registerDetail, { openModal: openDetailModal }] = useModal();

      function showDetailModal(record) {
        console.error(123, record);
        openDetailModal(true, { record: unref(record), isUpdate: true });
      }

      const tagIdCheck = ref('');
      const messageList = ref([]);

      const queryMessageGroup = () => {
        tagIdCheck.value = '';
        return getMessageGroup({ messageTypeNameList: null }, routerType.value)
          .then((res) => {
            messageList.value = res;
            tagIdCheck.value = res[0]?.eventClassName;
          })
          .catch((err) => {
            console.log('err:', err);
          });
      };

      watch(tagIdCheck, () => {
        reloadWarnData();
      });

      const reloadWarnData = () => {
        if (!tagIdCheck.value) return;

        let params = {
          filer: {
            messageClassName: tagIdCheck.value,
          },
        };
        warnMessageRef.value.reload(params, activeKey.value, routerType.value);
      };

      const getRouterType = (str) => {
        if (str.includes('ipms_ly1_a')) {
          routerType.value = 'ipms_ly1_a';
        } else if (str.includes('ipms_ly1_b')) {
          routerType.value = 'ipms_ly1_b';
        } else if (str.includes('ipms_ly1_c')) {
          routerType.value = 'ipms_ly1_c';
        } else if (str.includes('ipms_ly5_c')) {
          routerType.value = 'ipms_ly5_c';
        } else {
          routerType.value = '';
        }
      };
      const showWarningMsg = ref(true);
      const showTempWarningMsg = ref(false);
      listenerRouteChange((route) => {
        const isExits = route.path.includes('ipms');
        showWarningMsg.value = isExits;
        if (isExits) {
          activeKey.value = 'warn';
        } else {
          activeKey.value = 'sys';
        }

        let tempIsExits = route.path.includes('temp-monitor');
        if (!tempIsExits) {
          const tempWhiteList = getDictItemsByCode('portal_temp_ipms_link_id_white_list');
          tempWhiteList.map((item) => {
            if (route.fullPath.includes(item.value)) {
              tempIsExits = true;
            }
          });
        }
        showTempWarningMsg.value = tempIsExits;
        if (tempIsExits) {
          activeKey.value = 'tempWrap';
        } else {
          activeKey.value = 'sys';
        }

        getRouterType(route.path);
      });

      const readButtonClick = () => {
        updateReadFlag().then((r) => {
          reloadWarnData();
          message.success('已读设置成功');
        });
      };

      return {
        conditionStr,
        regModal,
        getSelectedUser,
        openSelectPerson,
        clearSearchParamsUser,
        clearAll,

        registerModal,
        activeKey,
        handleChangePanel,
        handleChangeTab,

        showSearch,
        searchParams,
        handleChangeSearchPerson,
        dateTags,
        handleClickDateTag,
        showRangeDate,
        searchRangeDate,
        handleChangeSearchDate,
        closeModal,
        hrefThenClose,

        sysMessageRef,
        warnMessageRef,
        registerDetail,
        showDetailModal,
        tagIdCheck,
        messageList,
        showWarningMsg,
        showTempWarningMsg,
        readButtonClick,
      };
    },
  };
</script>

<style lang="less">
  @keyframes move22 {
    0% {
      transform: translateY(0px);
    }

    100% {
      transform: translateY(600px);
    }
  }

  .sys-msg-modal {
    .ant-modal-header {
      padding-bottom: 0;
      padding-top: 6px;
      padding-right: 12px;
    }

    .ant-modal-body {
      height: 550px;
      overflow-y: auto;
    }

    .ant-modal {
      position: absolute;
      right: 10px;
      top: calc(100% - 600px);
      /*      animation-name: move22;
  animation-duration:0.8s;
  animation-timing-function:ease;*/
    }
  }

  .sys-msg-modal-title {
    position: relative;

    .close-icon-box {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 1;
    }

    .title {
      display: inline-block;
      width: 120px;
    }

    .icon-right {
      display: none;
      width: 220px;
      vertical-align: top;

      .icons {
        display: flex;
        height: 100%;
        flex-direction: row;
        justify-content: flex-end;

        > span.anticon {
          padding: 10px;
          display: inline-block;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }

          &:active {
            color: #1890ff;
          }
        }

        > span.filtera {
          background-color: #d3eafd;
          border-radius: 4px;
          cursor: pointer;
          height: 27px;
          padding-top: 7px;
          margin-top: 3px;
          line-height: 25px;
          color: #2196f3;
          display: flex;

          > span.anticon {
            height: 30px;
            line-height: 9px;
            display: inline-block;
          }
        }
      }
    }

    .ant-tabs-nav-wrap {
      display: inline-block;
      width: calc(100% - 340px);
    }

    .ant-tabs-nav-scroll {
      text-align: center;
      font-size: 14px;
      font-weight: normal;
    }
  }

  .sys-message-card {
    .tag-wrap {
      display: flex;
      justify-content: space-between;

      .tag-box {
        text-align: center;
        border-radius: 2px;
        padding: 3px 10px;
        margin-bottom: 5px;
      }
    }
  }

  .search-label {
    font-weight: 500;
    font-size: 14px !important;
    color: #757575 !important;
    display: inline-block;
    margin-right: 15px !important;
  }

  .search-date {
    display: flex;
    min-width: 0;
    margin-top: 15px;

    .date-label {
      margin-top: 4px;
      font-weight: 500;
      font-size: 14px !important;
      color: #757575 !important;
      margin-right: 15px !important;
    }

    .date-tags {
      display: -ms-flexbox;
      display: flex;
      display: -webkit-flex;
      -ms-flex-direction: column;
      -webkit-flex-direction: column;
      flex-direction: column;
      -webkit-flex: 1;
      flex: 1;
      -ms-flex: 1;

      .tags-container {
        display: flex;
        min-width: 0;
        -webkit-flex: 1;
        flex: 1;
        -ms-flex: 1;
        flex-wrap: wrap;

        .tag {
          background-color: #f5f5f5;
          border-radius: 17px;
          font-size: 13px;
          margin-bottom: 10px;
          margin-right: 10px;
          padding: 6px 12px;
          cursor: pointer;

          &.active {
            background-color: #d3eafd !important;
            color: #2196f3;
          }
        }
      }
    }
  }

  .selected-user {
    background: #f5f5f5;
    padding: 2px 14px;
    border-radius: 30px;

    .clear-user-icon {
      margin-left: 5px;

      .anticon {
        &:hover {
          color: #2196f3;
        }
      }
    }
  }
</style>
