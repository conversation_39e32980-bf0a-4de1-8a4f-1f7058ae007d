<template>
  <div class="scheduleBox">
    <div class="smallTit"> 联系我们 </div>
    <div class="contactUsBox">
      <div class="contactInfo">无论是产品使用的疑问、对产品的建议或者是吐槽，您都可以通过提交反馈与我们进行联系，我们必将重视，尽快响应。</div>
      <div class="contactImg">
        <img :src="Contact" alt="" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import Contact from '/@/assets/images/homeImg/Contact.png';
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  //   点击我的日常跳转更多
  function handleMore() {
    router.push('/portal');
  }
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 242px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px 20px;
    margin-top: 16px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
    }
    .contactUsBox {
      width: 100%;
      height: auto;
      position: relative;
      .contactInfo {
        width: calc(100% - 212px);
        font-size: 16px;
        line-height: 32px;
        color: #4e5969;
      }
      .contactImg {
        width: 244px;
        height: 163px;
        position: absolute;
        right: -24px;
        top: -20px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
</style>
