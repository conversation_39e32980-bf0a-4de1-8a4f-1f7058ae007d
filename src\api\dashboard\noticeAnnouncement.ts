/**
 * @Name noticeAnnouncement
 * @Description：通知公告
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 11:11
 * @FilePath: src\api\dashboard\noticeAnnouncement.ts
 */

import { NoticesListGetResultModel, NoticesParams } from '/@/api/dashboard/model/noticeAnnouncementModel';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  SYS_NOTICE_LIST = '/sys/annountCement/vue3ListAll',
}

// Get personal center-basic settings

export const getSysNoticesList = (params) =>
  defHttp.get<NoticesListGetResultModel>({
    url: Api.SYS_NOTICE_LIST,
    params,
  });
