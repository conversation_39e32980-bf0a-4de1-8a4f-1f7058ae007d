<template>
  <div class="menuSettingContainer">
    <div class="header">
      <div class="title"> 我的常用</div>
      <div class="buttons">
        <a-button type="link" @click="showDrawer">
          <img class="operator-icon" :src="add" />
          <span>添加</span>
        </a-button>
        <a-button type="link" v-if="!manageFlg && currentMenus.length" @click="manageFlg = true">
          <img class="operator-icon" :src="manage" />
          <span>管理</span>
        </a-button>
        <a-button type="link" v-if="manageFlg" @click="saveEvent">
          <img class="operator-icon w13" :src="save" />
          <span>保存</span>
        </a-button>
      </div>
    </div>

    <draggable v-if="currentMenus.length" :disabled="!manageFlg" v-model="currentMenus" group="app" item-key="key" class="menu-list" animation="500">
      <template #item="{ element }">
        <span
          class="item"
          @click="() => !manageFlg && changePage(element)"
          :title="element.name"
          :draggable="!element.isConst"
          v-if="(manageFlg && !element.isConst) || !manageFlg"
        >
          <span class="icon-container">
            <img class="icon" :class="manageFlg ? 'move' : ''" :src="element.img" />
            <img class="icon-close" @click="() => deleteItem(element)" v-if="manageFlg" :src="close" />
            <!--            <DragOutlined  v-if="manageFlg" class="move-icon"/>-->
          </span>
          <span class="title">{{ element.name }}</span>
        </span>
      </template>
    </draggable>
    <div v-if="!currentMenus.length && requestFlg" style="text-align: center">
      <img :src="empty" width="200" style="margin-top: 20px" />
      <div style="font-size: 16px; margin-top: 20px">暂无常用</div>
    </div>
    <a-modal
      :bodyStyle="{
        textAlign: 'center',
      }"
      v-model:visible="visible"
      :width="634"
      :destroyOnClose="true"
      centered
      closable
    >
      <template #title>
        <div style="display: flex; justify-content: center; align-items: center"> 我的常用</div>
      </template>

      <div class="menus-container">
        <div class="left-container">
          <div
            @click="() => selectParent(parent.key)"
            class="item"
            :key="parent.key"
            v-for="parent in allMenus"
            :class="currentParent === parent.key ? 'active' : ''"
            >{{ parent.title }}
          </div>
        </div>
        <div class="right-container" ref="rightDom">
          <div
            @click="() => !currentMenus.some((j) => j.id === child.id) && selectChild(child)"
            class="item"
            :key="child.id"
            v-for="child in allMenus.find((i) => i.key === currentParent).children"
          >
            <div class="title">{{ child.meta.title }}</div>
            <div class="checkbox">
              <a-checkbox v-model:checked="child.checked" :disabled="currentMenus.some((j) => j.id === child.id)"></a-checkbox>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: right; align-items: center">
          <a-button @click="visible = false">取消</a-button>
          <a-button type="primary" @click="okModal">确定</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import draggable from 'vuedraggable';

  const { bus } = WujieVue;
  import icon1 from '/@/assets/images/myCommon/1.png';
  import icon2 from '/@/assets/images/myCommon/2.png';
  import icon3 from '/@/assets/images/myCommon/3.png';
  import icon4 from '/@/assets/images/myCommon/4.png';
  import icon5 from '/@/assets/images/myCommon/5.png';
  import icon6 from '/@/assets/images/myCommon/6.png';
  import icon7 from '/@/assets/images/myCommon/7.png';
  import icon8 from '/@/assets/images/myCommon/8.png';
  import icon9 from '/@/assets/images/myCommon/9.png';
  import icon10 from '/@/assets/images/myCommon/10.png';
  import icon11 from '/@/assets/images/myCommon/11.png';
  import icon12 from '/@/assets/images/myCommon/12.png';
  import icon13 from '/@/assets/images/myCommon/13.png';
  import add from '/@/assets/images/myCommon/add.png';
  import close from '/@/assets/images/myCommon/close.png';
  import empty from '/@/assets/images/myCommon/empty.png';
  import manage from '/@/assets/images/myCommon/manage.png';
  import save from '/@/assets/images/myCommon/save.png';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getUserPermissionByTokenGroupByApp, saveOrUpdateFavorMenu, getFavorMenuList } from '@/views/dashboard/HomePage/index.api';
  import { useRouter } from 'vue-router';
  import WujieVue from 'wujie-vue3';
  import { usePermissionStoreWithOut } from '/@/store/modules/permission';
  import { useUserStore } from '@/store/modules/user';
  import { initMenuList, setPermissionId } from '@/utils/accessLogUtil';
  import { indexShowApps } from '/@/api/common/api';

  const permissionStore = usePermissionStoreWithOut();

  const icons = [icon1, icon2, icon3, icon4, icon5, icon6, icon7, icon8, icon9, icon10, icon11, icon12, icon13];
  const { createMessage } = useMessage();
  const visible = ref<boolean>(false);
  const manageFlg = ref<boolean>(false);
  const allMenus = ref<any[]>([]);
  const rightDom = ref(null);
  const currentMenus = ref<{ id: string; name: string; url: string }[]>([]);
  const constMenuKeys = [];
  const idToCode = ref<{ [x: string]: any }>({});
  const requestFlg = ref(false);

  /**
   * TODO 写在前面
   *
   * 这些code太乱了, 时间有限, 实在没精力整合这些code, 所以....留待后人重构
   *
   * 1. 后端会存一个code, 这个code对应每个系统, 用于识别每个子应用对应的菜单
   * 2. 每个子应用在为前端注册时有一个code, 用于wujie注册时使用
   * 3. 父应用访问子应用时, 每个子应用对应一个code, 用于加载不同的子应用
   * 4. 每个子应用的publicPath, 对应一个code, 在服务器端来区分访问的是哪个应用
   *
   * 可悲的是, 这些code, 有的一致, 有的不一致, 关键是不同应用的code, 在不同情况下, 规则还不一致
   *
   * 初期设计该系统的人, 真的很.....
   */

  // 存在后端的code - 顺序重要 - 作为排序
  let codeMap = {
    // 'ly1-b-ipms': '原料一厂B系列',
    // 'ly1-c-ipms': '原料二厂C系列',
    // 'ly1-a-ipms': '原料三厂A系列',
    // 'ly5-c-ipms': '铝五三厂C系列',
    // slot_health_manage: '槽健康管理',
    // anode_web: '阳极全流程',
    // pdos_web: '工序数字化',
    // bgos_web: '抬包全流程',
    // qc_web: '质检',
    // purify_web: '净化输料',
    // system: '系统管理',
  };

  // TODO: 新增应用需在此处增加应用编码，否则应用菜单无法跳转
  // 前端的code 放在/middle/后
  const appMiddleMap = {
    system: 'system',
    slot_health_manage: 'slot-health-manage-web',
    anode_web: 'anode-web',
    pdos_web: 'pdos-web',
    bgos_web: 'bgos-web',
    qc_web: 'qc-web',
    purify_web: 'purify-web',
    'ly1-b-ipms': 'ipms_ly1_b',
    'ly1-c-ipms': 'ipms_ly1_c',
    'ly1-a-ipms': 'ipms_ly1_a',
    'ly5-c-ipms': 'ipms_ly5_c',
    'ly3-c-ipms': 'ipms_ly3_c',
    'ly3-qc-web': 'ly3-qc-web',
    'ly3_slot_health_manage': 'ly3-slot-health-manage-web',
  };
  // 前端code 放在 ?后面
  const appSearchMap = {
    system: 'system',
    slot_health_manage: 'slot_health_manage_web',
    anode_web: 'anode_web',
    pdos_web: 'pdos_web',
    bgos_web: 'bgos_web',
    qc_web: 'qc_web',
    purify_web: 'purify_web',
    'ly1-b-ipms': 'ipms_ly1_b',
    'ly1-c-ipms': 'ipms_ly1_c',
    'ly1-a-ipms': 'ipms_ly1_a',
    'ly5-c-ipms': 'ipms_ly5_c',
    'ly3-c-ipms': 'ipms_ly3_c',
    'ly3-qc-web': 'ly3-qc-web',
    'ly3_slot_health_manage': 'ly3-slot-health-manage-web',
  };

  const router = useRouter();

  const currentParent = ref(null);

  onMounted(async () => {
    await getCodeMap();
    initMenuList();
    // 查询所有有权限的菜单
    await getAllMenu();
    // 查询当前收藏的菜单
    await getFavorMenu();

    requestFlg.value = true;
  });

  // 获取所有有权限的菜单
  const getCodeMap = async () => {
    const userStore = useUserStore();
    const res = await indexShowApps({ corp: userStore.getTenant });
    console.log('🚀 ~ menuSetting.vue:196 ~ getCodeMap ~ res:', res);
    const allApps = res.reduce((item, next) => {
      item = [...item, ...next.apps];
      return item;
    }, []);
    // codeMap = allApps.reduce((item, next) => {
    //   item[next.code] = next.name;
    //   return item;
    // })
    allApps.forEach((item) => {
      codeMap[item.appCode] = item.appName;
      // appSearchMap[item.code] = item.searchCode;
    });

    console.log('🚀 ~ menuSetting.vue:207 ~ codeMap=allApps.reduce ~ codeMap:', codeMap);

    console.log('🚀 ~ menuSetting.vue:202 ~ allApps ~ allApps:', allApps);
  };

  const changePage = ({ id, url = '', isConst }) => {
    if (!url) {
      return;
    }

    const code = idToCode.value[id];
    console.log('🛸👽 ~ menuSetting.vue ~ changePage ~ idToCode:', idToCode);

    let middleCode = appMiddleMap[code];
    console.log('🛸👽 ~ menuSetting.vue ~ changePage ~ appMiddleMap:', appMiddleMap);

    console.log('🛸👽 ~ menuSetting.vue ~ changePage ~ middleCode:', middleCode);

    let searchCode = appSearchMap[code];
    console.log('🛸👽 ~ menuSetting.vue ~ changePage ~ appSearchMap:', appSearchMap);

    if (url.indexOf('http') === 0 || url.indexOf('//') === 0) {
      window.open(url);
      return;
    }

    let path = '';
    if (code === 'ly1-b-ipms') {
      path = `/ly1-b-ipms-web${url}`;
    } else if (code === 'ly1-c-ipms') {
      path = `/ly1-c-ipms-web${url}`;
    } else if (code === 'ly1-a-ipms') {
      path = `/ly1-a-ipms-web${url}`;
    } else if (code === 'ly5-c-ipms') {
      path = `/ly5-c-ipms-web${url}`;
    } else {
      path = `/${middleCode}${url}`;
    }

    if (isConst) {
      if (id == 'phase3-web-smart-center') {
        bus.$emit(`phase3-web-routeChange`, url);
        router.push(`/middle/phase3-web?phase3_web=` + encodeURIComponent(url));
      }
    } else {
      bus.$emit(`${searchCode}-routeChange`, url);
      router.push(`/middle/${middleCode}?${searchCode}=` + encodeURIComponent(path));
    }
  };
  let protalAuth = [];

  const getAllMenu = async () => {
    const keys = Object.keys(codeMap);
    keys.push('portal');
    const res = await getUserPermissionByTokenGroupByApp({
      appCodeList: keys.join(','),
    });
    protalAuth = res.portal.auth;
    keys.forEach((key) => {
      if (res[key]) {
        const _menus = res[key].menu as any[];
        const items: any[] = [];
        while (_menus.length) {
          const item = _menus.shift();
          const { id, path, children = [], hidden = false } = item;
          if (hidden) {
            continue;
          }
          if (children && children.length) {
            _menus.unshift(...children);
          } else if (id && path) {
            items.push(item);
          }
        }
        items.length &&
          items.forEach(({ id }) => {
            idToCode.value[id] = key;
          });
        items.length &&
          allMenus.value.push({
            title: codeMap[key],
            key,
            children: items,
          });
      }
    });

    currentParent.value = allMenus.value[0].key;
    // allMenus 当前所有有权限的菜单
  };

  // 当前收藏的菜单
  const getFavorMenu = async () => {
    const res = await getFavorMenuList();
    currentMenus.value = res
      .map((i) => {
        return {
          id: i.id,
          name: i.name,
          url: i.url,
          img: icons.shift(),
        };
      })
      .filter((i) => !!idToCode.value[i.id]);
    initConstMenu(protalAuth);
  };

  const initConstMenu = () => {
    if (protalAuth.find((i) => i.action == 'portal:commonMenu:phase3-web-smart-center')) {
      const phase3WebSmartCenter = 'phase3-web-smart-center';
      constMenuKeys.push(phase3WebSmartCenter);
      currentMenus.value.unshift({
        id: phase3WebSmartCenter,
        name: '智慧中心',
        url: '/smartAlu/center/index',
        isConst: true,
        img: icons.shift(),
      });
    }

    if (
      protalAuth.find((i) => {
        setPermissionId(i.id);
        return i.action == 'portal:commonMenu:phase3-web-digital-human';
      })
    ) {
      const userStore = useUserStore();
      if (userStore.getUserInfo.username !== 'super_admin') {
        permissionStore.setShowBall(true);
      }
    }
  };

  //切换子系统,显示对应子系统的菜单
  const selectParent = (key) => {
    if (currentParent.value === key) {
      return;
    }
    rightDom.value.scrollTop = 0;
    currentParent.value = key;
  };
  // 点选子菜单
  const selectChild = (item) => {
    item.checked = !item.checked;
  };

  // 打开菜单选择
  const showDrawer = () => {
    if (currentMenus.value.length >= 12) {
      createMessage.warn('最多仅能添加12个常用菜单');
      return;
    }
    if (!Object.keys(idToCode.value).length) {
      createMessage.warn('暂无权限访问，可联系管理员配置');
      return;
    }

    const currentIds = currentMenus.value.map((i) => i.id);

    Object.keys(codeMap).forEach((key) => {
      ((allMenus.value.find((i) => i.key === key) || {}).children || []).forEach((child) => {
        child.checked = currentIds.indexOf(child.id) >= 0;
      });
    });
    visible.value = true;
  };
  const saveEvent = async () => {
    const ids = currentMenus.value.map((i) => i.id).filter((id) => !constMenuKeys.includes(id));
    await saveOrUpdateFavorMenu({
      ids,
    });
    manageFlg.value = false;
  };
  const deleteItem = (element) => {
    const { id } = element;
    const index = currentMenus.value.findIndex((item) => item.id === id);
    const img = currentMenus.value[index].img;
    currentMenus.value.splice(index, 1);
    icons.push(img);
  };

  // 保存菜单
  const okModal = async () => {
    const selectMenus: any[] = [];
    Object.keys(codeMap).forEach((key) => {
      ((allMenus.value.find((i) => i.key === key) || {}).children || []).forEach((child) => {
        if (child.checked) {
          selectMenus.push({
            id: child.id,
            name: child.title || child.meta.title,
            url: child.path,
          });
        }
      });
    });

    if ([...new Set([...selectMenus.map((i) => i.id), ...currentMenus.value.map((i) => i.id)])].length > 12) {
      createMessage.warn('最多仅能添加12个常用菜单');
      return;
    }

    // 当前配置中 直接追加 不调用接口
    const oldItems = currentMenus.value;
    const currentItems = selectMenus.filter((i) => oldItems.every((j) => j.id !== i.id));
    currentMenus.value.push(
      ...currentItems.map((i) => {
        return {
          ...i,
          img: icons.shift(),
        };
      })
    );
    const ids = currentMenus.value.map((i) => i.id);
    await saveOrUpdateFavorMenu({
      ids,
    });
    manageFlg.value = false;
    visible.value = false;
  };
</script>

<style scoped lang="less">
  .menus-container {
    height: 75vh;

    & > .left-container {
      display: inline-block;
      height: 100%;
      overflow-y: auto;
      width: 50%;
      background: #f0f0f0;
      font-size: 20px;
      font-weight: bold;

      & > .item {
        text-align: center;
        padding: 14px 20px;
        cursor: default;

        &.active {
          background: #f8f8f8;
          color: #1677ff;
        }
      }
    }

    & > .right-container {
      display: inline-block;
      height: 100%;
      overflow-y: auto;
      width: 50%;
      background: #fff;

      & > .item {
        color: #24272d;
        font-size: 16px;
        padding: 12px 20px;

        & > .title {
          text-align: center;
          cursor: default;
          width: calc(100% - 40px);
          display: inline-block;
        }

        & > .checkbox {
          display: inline-block;
        }
      }
    }
  }

  .menuSettingContainer {
    background: #fff;
    padding: 20px;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;

    & > .header {
      display: flex;

      & > .title {
        font-size: 22px;
        font-weight: bold;
      }

      & > .buttons {
        flex: 1;
        text-align: right;

        & .operator-icon {
          position: relative;
          top: -2px;
          width: 16px;
          margin-right: 5px;

          &.w13 {
            width: 13px;
            margin-right: 8px;
          }
        }
      }
    }

    & > .menu-list {
      width: 100%;
      padding-top: 20px;

      //padding:30px 10px 0 10px;
      //display: flex;
      //justify-content: space-around;
      //flex-wrap: wrap;
      & > .item {
        width: 25%;
        margin: 5px 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        & > .icon-container {
          position: relative;

          & .icon {
            width: 44px;

            &.move {
              cursor: move;
            }
          }

          & .icon-close {
            position: absolute;
            width: 14px;
            top: -4px;
            right: -4px;
            cursor: default;
            z-index: 10;
          }

          //& > .close-icon{
          //  font-size:12px;
          //  position: absolute;
          //  right:-4px;
          //  top:-4px;
          //  padding:5px;
          //}
          //& > .move-icon{
          //  font-size:12px;
          //  position: absolute;
          //  right:0;
          //  top:50%;
          //  transform: translateY(-50%);
          //}
        }

        & > .title {
          font-size: 14px;
          line-height: 22px;
          color: #1d2129;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
          text-align: center;
        }

        cursor: pointer;
        box-sizing: border-box;
      }
    }
  }
</style>
