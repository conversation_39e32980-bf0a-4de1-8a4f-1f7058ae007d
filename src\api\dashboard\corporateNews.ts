/**
 * @Name corporateNews
 * @Description：企业新闻
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/5 17:33
 * @FilePath: src\api\dashboard\corporateNews.ts
 */

import { defHttp } from '/@/utils/http/axios';
import { NewsListGetResultModel, NewsParams } from './model/corporateNewsModel';

enum Api {
  SYS_NEWS_LIST = '/sys/sysNews/list',
}

// Get personal center-basic settings

export const getSysNewsList = (params: NewsParams) => defHttp.get<NewsListGetResultModel>({
  url: Api.SYS_NEWS_LIST,
  params
});

