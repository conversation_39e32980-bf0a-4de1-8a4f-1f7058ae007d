<template>
  <div>
    <BreadCrumb />
    <BasicTable @register="registerTable" :rowSelection="null">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getActions(record)" />
      </template>
    </BasicTable>
    <NoticeModal @register="register" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" name="system-notice" setup>
import { ref, unref, onMounted } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, searchFormSchema } from './calendarDetails.data';
import { getList, getExportUrl, deleteOne } from './calendarDetails.api';
import { useListPage } from '/@/hooks/system/useListPage';
import { useRouter } from 'vue-router';
import NoticeModal from './NoticeModal.vue';
import BreadCrumb from '@/views/dashboard/notice/components/breadCrumb/index.vue';

const { currentRoute } = useRouter();
const [register, { openModal }] = useModal();

// 列表页面公共参数、方法
const { prefixCls, tableContext, onExportXls } = useListPage({
  designScope: 'calendarDetails-template',
  tableProps: {
    title: '消息通知',
    api: getList,
    columns: columns,
    formConfig: {
      schemas: searchFormSchema,
    },
  },
  exportConfig: {
    name: '日程日志详情列表',
    url: getExportUrl,
  },
});

const [registerTable, { getForm, reload }, { rowSelection, selectedRowKeys }] = tableContext;
onMounted(() => {
  const { setFieldsValue } = getForm();
  setFieldsValue({
    sendTime: unref(currentRoute)?.params?.date,
  });
});
/**
 * 编辑事件
 */
function handleEdit(record) {
  openModal(true, {
    record,
    isUpdate: true,
  });
}
// 删除
const handleDelete = async (record) => {
  const isWorkLog = record?.type === 2;
  await deleteOne({ id: record.id }, reload, isWorkLog);
};
/**
 * 操作列定义
 * @param record
 */
function getActions(record) {
  return [
    {
      label: '修改',
      onClick: handleEdit.bind(null, record),
    },
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
    },
  ];
}
const handleSuccess = () => {
  reload();
};
</script>
