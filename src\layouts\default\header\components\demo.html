<!--
 * @Description: 
 * @Autor: yst
 * @Date: 2024-10-24 11:18:30
 * @LastEditors: yst
 * @LastEditTime: 2024-10-24 11:18:33
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Webpage Screenshot</title>
  </head>
  <body>
    <div style="width: 100%; height: 400px; background-color: lightblue">
      <h1>Hello, World!</h1>
      <p>This is a demo page to capture the current webpage.</p>
    </div>

    <button onclick="capturePage()">Capture Webpage</button>
    < img id="screenshot" src="" alt="Screenshot will appear here" />

    <script>
      async function capturePage() {
        try {
          // Capture the screen
          const stream = await navigator.mediaDevices.getDisplayMedia({
            video: {
              mediaSource: 'screen',
              cursor: 'always',
            },
          });

          // Create a video element to play the captured stream
          const videoElement = document.createElement('video');
          videoElement.srcObject = stream;
          videoElement.play();

          // Once the video starts playing, capture a frame
          videoElement.onloadedmetadata = async () => {
            const canvas = document.createElement('canvas');
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
            const context = canvas.getContext('2d');

            // Draw the video frame into the canvas
            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            // Convert the canvas to an image (data URL) and display it
            const dataURL = canvas.toDataURL();
            document.getElementById('screenshot').src = dataURL;

            // Stop the stream after capturing
            stream.getTracks().forEach((track) => track.stop());
          };
        } catch (err) {
          console.error('Error capturing the webpage: ', err);
        }
      }
    </script>
  </body>
</html>
