<template>
  <div :style="menuWidth" style="float: left; padding-left: 10px" class="file-menu">
    <div class="uploadFile">
      <div class="upload-icon">
        <a-button v-if="!(props.typeData === 'star')" type="primary" preIcon="ant-design:upload-outlined" @click="onUpload"> 导入</a-button>
      </div></div
    >
    <a-table
      :loading="loading"
      :pagination="iPagination"
      rowKey="id"
      :columns="tableColumns"
      :dataSource="dataSource"
      :customRow="customRow"
      @change="handleTableChange"
    >
      <template v-slot:bodyCell="{ column, record, text }">
        <!-- {{ column }} -->
        <template v-if="column.key === 'fileName'">
          <div class="editable-cell">
            <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
              <a-input
                :ref="(el) => setItemRef(el, `rename${record.id}`)"
                style="width: 90%"
                v-model:value="editableData[record.id].fileName"
                @blur="inputSave(record.id)"
                @pressEnter="inputSave(record.id)"
              />
            </div>
            <div v-else class="editable-cell-text-wrapper">
              <div style="display: flex">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap" v-if="record.fileType !== 'folder'" class="pointer">
                  <img class="file-image" v-if="record.fileType === 'image'" :src="getImageSrc(record.url)" @click="handleTextClick(record)" />
                  <Icon
                    v-else-if="record.fileType === 'excel'"
                    class="file-icon"
                    icon="ant-design:file-excel-outlined"
                    style="color: rgb(98, 187, 55)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'pdf'"
                    class="file-icon"
                    icon="ant-design:file-pdf-outlined"
                    style="color: rgb(211, 47, 47)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'doc'"
                    class="file-icon"
                    icon="ant-design:file-word-outlined"
                    style="color: rgb(68, 138, 255)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'pp'"
                    class="file-icon"
                    icon="ant-design:file-ppt-outlined"
                    style="color: rgb(245, 124, 0)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'video'"
                    class="file-icon"
                    icon="ant-design:play-square-outlined"
                    style="color: rgb(119, 87, 188)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'text'"
                    class="file-icon"
                    icon="ant-design:file-text-outlined"
                    style="color: rgb(41, 211, 178)"
                    @click="handleTextClick(record)"
                  />
                  <Icon
                    v-else-if="record.fileType === 'zip'"
                    class="file-icon"
                    icon="ant-design:file-zip-outlined"
                    style="color: rgb(253, 202, 7)"
                    @click="handleTextClick(record)"
                  />
                  <img :src="LinkImg" v-else-if="(record.ext = 'url')" class="file-image" @click="handleTextClick(record)" />
                  <Icon v-else class="file-icon" icon="ant-design:file-unknown-outlined" @click="handleTextClick(record)" />

                  <span
                    v-if="record.fileType === 'image' || record.ext === 'url'"
                    style="margin-left: 8px"
                    class="file-text"
                    @click="handleTextClick(record)"
                  >
                    {{ text + (record.ext ? '.' + record.ext : '') }}
                  </span>
                  <span v-else style="margin-left: 30px" class="file-text" @click="handleTextClick(record)">
                    {{ text + (record.ext ? '.' + record.ext : '') }}
                  </span>
                </div>
                <span style="">
                  <Icon v-if="record.izStar === '1'" icon="ant-design:star-outlined" style="color: #ff9800; margin-left: 4px; margin-top: 10px"
                /></span>
              </div>
              <div class="editable-cell-icon" v-if="selectedRowKeys.length < 2">
                <Icon style="top: -6px" icon="ant-design:star-outlined" class="header-icon-color pointer" @click="starUpdate(record)" />
                <Icon
                  style="top: -6px; left: 10px"
                  class="header-icon-color pointer"
                  icon="ant-design:cloud-download-outlined"
                  @click="downFile(record)"
                />
                <a-dropdown :trigger="['click']">
                  <Icon
                    class="header-icon-color pointer"
                    v-if="
                      (authority === 'editable' && record.enableDown === '1') ||
                      record.createBy === createBy ||
                      createBy === 'super_admin' ||
                      createBy === 'admin'
                    "
                    style="top: -6px; left: 20px"
                    icon="ant-design:ellipsis-outlined"
                    @click.prevent
                  />
                  <template #overlay>
                    <a-menu class="menu-more" v-if="delFlag === '1'">
                      <span
                        style="text-align: center; display: block"
                        v-if="authority === 'readonly' || (authority === 'editable' && record.createBy !== createBy)"
                        >暂无权限</span
                      >
                      <a-menu-item
                        key="0"
                        v-if="(authority !== 'readonly' && authority !== 'editable') || (authority === 'editable' && record.createBy === createBy)"
                      >
                        <div @click="reductionFile(record)">
                          <Icon class="table-icon-color pointer" icon="ant-design:rollback-outlined" />
                          <span class="table-font-size">还原</span>
                        </div>
                      </a-menu-item>
                      <a-menu-item
                        key="0"
                        v-if="(authority !== 'readonly' && authority !== 'editable') || (authority === 'editable' && record.createBy === createBy)"
                      >
                        <div @click="removeCompletelyFile(record.id)">
                          <Icon class="table-icon-color pointer" icon="ant-design:delete-filled" />
                          <span class="table-font-size">彻底删除</span>
                        </div>
                      </a-menu-item>
                    </a-menu>
                    <a-menu class="menu-more" v-else>
                      <a-menu-item
                        key="0"
                        v-if="
                          record.ext === 'url' &&
                          ((authority === 'editable' && record.enableUpdat === '1') || authority === 'owner' || authority === 'admin')
                        "
                      >
                        <div @click="editLink(record)">
                          <Icon class="table-icon-color pointer" icon="ant-design:edit-outlined" />
                          <span class="table-font-size">编辑</span>
                        </div>
                      </a-menu-item>

                      <a-menu-divider v-if="record.izFolder === '0'" />
                      <a-menu-item key="1">
                        <div @click="inputEdit(record.id)">
                          <Icon class="table-icon-color pointer" icon="ant-design:edit-outlined" />
                          <span class="table-font-size">重命名</span>
                        </div>
                      </a-menu-item>
                      <a-menu-item key="6" v-if="authority !== 'readonly' && (authority !== 'editable' || record.createBy === createBy)">
                        <div @click="deleteFile(record.id)">
                          <Icon class="table-icon-color pointer" icon="ant-design:delete-outlined" />
                          <span class="table-font-size">删除</span>
                        </div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <uploadFileModal @register="registerModalJimport" :url="getFilesUploadSysFileUrl" :uploadSetting="state.uploadSettings" @ok="getFileList" />
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { ref, unref, onMounted, reactive, nextTick, computed, watch } from 'vue';
import { columns, miniColumns } from '../index.data';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';
import { useModal } from '@/components/Modal';
import uploadFileModal from './uploadFileModal.vue';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
import { filesDelete, getFilesListApi, addFavo, listFavo, deleteFavo, getFilesUploadSysFileUrl, saveOrUpdateFiles, deleteFiles } from '../index.api';
import { useContextMenu } from '/@/hooks/web/useContextMenu';
import LinkImg from '/@/assets/images/link.png';
import { useGlobSetting } from '@/hooks/setting';
import { encryptByBase64 } from '@/utils/cipher';
const loading = ref<boolean>(false);
const glob = useGlobSetting();
const tableColumns = ref<any>(columns);
const dataSource = ref<any>([]);
const $message = useMessage();
const selectedRowKeys = ref<any>([]);
const [registerModalJimport, { openModal: openModalJimport }] = useModal();
const editableData = reactive({});
const activeItem = ref(null);
const rowData = ref<any>({});
// const menuWidth = ref<any>({ width: 'calc(100% - 260px)' });
tableColumns.value = columns;
const userStore = useUserStore();
const createBy = userStore.getUserInfo.username;
const authority = ref<string>('');
const serchContent = ref('');
const state = reactive({
  data: [],
  headerSearchValue: '',
  headerSearchState: false,
  loading: false,
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10,
  },
  getFilesListApiParma: {
    // izFolder: null,
    izRootFolder: 1,
    parentId: '',
  },
  breadCrumbs: [
    {
      id: 0,
      name: '根目录',
    },
  ],
  uploadSettings: {
    params: {
      bizPath: '',
      parentId: '',
    },
    // updataAccept: '*',
    updataAccept: '.xls,.xlsx,.doc,.docx,.ppt,.pptx,.gif,.jpg,.jpeg,.png,.txt,.avi,.mov,.rmvb,.rm,.flv,.mp4,.zip,.pdf',
    filesParamsName: 'files',
  },
});
function inputEdit(key) {
  editableData[key] = cloneDeep(dataSource.value.filter((item) => key === item.id)[0]);
}
let inputRefs: any = {};
const pageSetting = {
  total: 0,
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
};
const iPagination = ref<any>(pageSetting);

const props = defineProps({
  typeData: String,
  keyword: String,
});

onMounted(async () => {
  await getFileList();
});
//重命名
async function inputSave(key) {
  let params = {};
  params = { fileName: editableData[key].fileName, id: editableData[key].id };
  await saveOrUpdateFiles(params, true);
  delete editableData[key];
  await getFileList();
}
/**
 * 删除文件
 */
// async function deleteFile(id) {
//   await filesDelete({ id: id });
//   await getFileList();
// }
async function deleteFile(id) {
  await filesDelete({ id: id });
  // 在删除操作完成后检查当前页数据
  if (dataSource.value.length === 1 && iPagination.value.current > 1) {
    // 如果当前页只有一条数据（即删除的这一条），并且不在第一页，那么翻页到上一页
    iPagination.value.current -= 1;
  }
  await getFileList();
}
const rowSelection = computed(() => {
  return {
    selectedRowKeys: unref(selectedRowKeys),
    onChange: onSelectChange,
  };
});
const onSelectChange = (changableRowKeys) => {
  selectedRowKeys.value = changableRowKeys;
};
/**
 * 获取文件内容
 */
function getFileList() {
  const apiCall = props.typeData === 'star' ? listFavo : getFilesListApi;
  loading.value = true;
  let params = {
    izRootFolder: 1,
    parentId: '',
    fileName: `*${serchContent.value}*`,
    pageNo: iPagination.value.current,
    pageSize: iPagination.value.pageSize,
    column: 'createTime',
    order: 'desc',
  };
  dataSource.value = [];
  apiCall(params)
    .then((res) => {
      if (res.records && res.records.length > 0) {
        dataSource.value = res.records;
        iPagination.value.total = res.total; // 更新总记录数
      } else {
        dataSource.value = [];
        iPagination.value.total = 0; // 更新总记录数
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
/**
 * 渲染表格事件
 */
const customRow = (record) => {
  return {
    // onClick: (event) => {
    //   onRowClick(event, record);
    // },
    onDblclick: (event) => {
      onRowDblclick(event, record);
    },
  };
};
// async function onRowClick(event, record) {
//   await getFileList();
// }
const [createContextMenu] = useContextMenu();

// const downFile = (activeItem) => {
//   if (!unref(activeItem)?.id || unref(activeItem)?.fileType === 'folder') {
//     createMessage.warning('请选择文件');
//     return null;
//   }
//   window.open(unref(activeItem)?.url);
// };
const downFile = async (activeItem) => {
  if (!unref(activeItem)?.id || unref(activeItem)?.fileType === 'folder') {
    message.warning('请选择文件');
    return;
  }

  const url = unref(activeItem)?.url;
  const fileName = `${activeItem.fileName}${activeItem.ext ? `.${activeItem.ext}` : ''}`;

  if (!url) {
    message.error('文件URL不存在');
    return;
  }

  try {
    // 通过 fetch 获取文件数据
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('文件获取失败');
    }

    const blob = await response.blob();
    const downloadUrl = URL.createObjectURL(blob);

    // 创建一个链接元素
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName; // 指定下载的文件名
    link.style.display = 'none';

    // 触发点击事件
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放 URL 对象
    URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('下载文件失败', error);
    message.error('下载文件失败，请稍后再试');
  }
};
const handleTextClick = (item) => {
  let currentItem = item || unref(activeItem);
  if (!currentItem?.id || currentItem?.fileType === 'folder' || currentItem.editing || currentItem.editable) {
    message.warning('请选择文件');
    return null;
  }

  let url = `${'//ipms.corp.hongqiaocloud.com/kkfile/onlinePreview'}?url=` + encodeURIComponent(encryptByBase64(currentItem?.url));
  window.open(url, '_blank');
};
// const handleTextClick = (item) => {

//   let currentItem = item || unref(activeItem);
//   if (!currentItem?.id || currentItem?.fileType === 'folder' || currentItem.editing || currentItem.editable) {
//     message.warning('请选择文件');
//     return null;
//   }

//   // 确保URL编码处理正确，特别是对文件名的处理
//   let fileName = currentItem.fileName + (currentItem.ext ? '.' + currentItem.ext : '');
//   let encodedFileName = encodeURIComponent(fileName); // 对文件名进行URL编码

//   // 拼接整个URL，包括编码后的文件名
//   let url = `${'//ipms.corp.hongqiaocloud.com/kkfile/onlinePreview'}?name=${encodedFileName}&url=` + encodeURIComponent(encryptByBase64(currentItem.url));

//   window.open(url, '_blank');
// };
/**
 * 双击事件打开
 * @param event
 * @param record
 */
function onRowDblclick(event, record) {
  handleTextClick(record);
}
function setItemRef(el, key) {
  if (el) {
    nextTick(() => {
      inputRefs[key] = el;
      inputRefs[key].focus();
    });
  }
}
/**
 * 表格改变事件
 */
function handleTableChange(pagination) {
  iPagination.value = pagination;
  getFileList();
}

/**
 * 获取图片路径
 * @param url
 */
function getImageSrc(url) {
  return getFileAccessHttpUrl(url);
}
/**
 * 标星更新
 * @param record
 */
async function starUpdate(record) {
  //update-begin---author:wangshuai ---date:20221011  for：传递record参数，抽出通用方法--------------
  let izStar = record.izStar;
  let id = record.id;
  let params = { id: id };
  //update-end---author:wangshuai ---date:20221011  for：传递record参数，抽出通用方法--------------
  if (izStar == '1') {
    await deleteFavo({ id: id });
    getFileList();
  } else {
    await addFavo({ id: id });
    getFileList();
  }
}
const onUpload = () => {
  let length = state.breadCrumbs.length;
  let lastChild = state.breadCrumbs[length - 1];
  state.uploadSettings = {
    ...state.uploadSettings,
    params: {
      bizPath: `sys/manage${lastChild?.id ? `/${lastChild?.id}` : ''}`,
      parentId: lastChild?.id || '',
    },
  };
  openModalJimport(true);
};
watch(
  () => props.typeData,
  () => {
    // props.keyword=''
    serchContent.value = '';
    iPagination.value = { ...pageSetting };
    getFileList();
  }
);
watch(
  () => props.keyword,
  () => {
    serchContent.value = props.keyword || '';
    getFileList();
  }
);
</script>

<style scoped lang="less">
:deep(.ant-table-tbody > tr > td) {
  padding: 0 16px;
  height: 36px;
  line-height: 36px;
}
.uploadFile {
  overflow: hidden;
  background: white;
  height: 50px;
}
.upload-icon {
  margin: 10px 0px 0px 10px;
}

.add {
  color: white;
  background-color: #1e88e5;
  border-radius: 20px;
}

.upload-font-size {
  font-size: 13px;
}

.border-hide {
  border: none;
}

.editable-cell {
  position: relative;

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 90px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 30px;
    width: 40px;
    cursor: pointer;
    top: 10px;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: none !important;
    color: #999;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}

.editable-cell:hover .editable-cell-icon {
  display: inline-block !important;
}

.title-hover:hover {
  color: #1e88e5;
}

.file-icon {
  height: 28px;
  margin-top: 4px;
  position: absolute;
  vertical-align: top;
  width: 26px;
  left: 1px;
  :deep(svg) {
    width: 100% !important;
    height: 100% !important;
  }
}

.file-image {
  display: inline-block !important;
  height: 24px;
  margin-right: 2px;
  margin-top: 6px;
  vertical-align: top;
  width: 21px;
}
.pointer {
  cursor: pointer;
  // margin-left: 5px;
}

.file-text:hover {
  color: rgb(51, 51, 51);
  text-decoration: underline;
}
.file-menu {
  overflow: hidden;
  float: left;
  // margin-top: 10px;
}
:deep(.ant-table-selection-column) {
  padding: 0 !important;
}
.share {
  display: table;
  height: 150px;
  width: 100%;
}
.file-form {
  height: 200px;
  .file-item {
    margin-bottom: 24px;
    padding-left: 100px;
    position: relative;
  }

  .file-item-label {
    color: #9e9e9e;
    font-size: 14px;
    height: 36px;
    left: 0;
    line-height: 36px;
    position: absolute;
    text-align: right;
    width: 80px;
  }
  .file-item-content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    top: 6px;
  }
}
.closed-tip {
  color: #919191;
  font-size: 13px;
}
.share-input {
  width: 230px;
  padding: 0 12px;
  box-sizing: border-box;
  font-size: 14px;
  height: 34px;
}
.share-span {
  font-size: 13px;
  border-radius: 3px;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  height: 34px;
  line-height: 34px;
  margin-left: 12px;
  text-align: center;
  width: 68px;
  box-sizing: border-box;
  background-color: #1e88e5;
}
.header-icon {
  display: inline;
  margin-left: 22px;
}
.header-icon-color {
  color: #999;
  font-size: 20px !important;
  position: relative;
  top: 2px;
}
.header-icon-color:hover {
  color: #1e88e5 !important;
}
.table-icon-color {
  display: inline;
  font-size: 13px !important;
}
.table-font-size {
  font-size: 13px;
  margin-left: 10px;
}
.menu-more {
  z-index: 512;
  width: 156px;
  :deep(.ant-dropdown-menu-item) {
    width: 100%;
    height: 42px !important;
    margin: 0 !important;
    line-height: 42px !important;
  }
}
</style>
