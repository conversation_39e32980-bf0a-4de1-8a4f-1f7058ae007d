/**
 * 用户访问日志收集工具
 */
import dayjs from 'dayjs';
import { saveAccessLogRecord, saveSccessLogRecordByFetch } from '@/api/sys/accessLog';

let routeObj: any = {};
let prevData: any = null;
export let permissionId = null;

export function setPermissionId(id) {
  permissionId = id;
}

export function initMenuList() {
  addWindowsEvents();
}

export function collectLog(permissionId) {
  const id = generate32BitString();
  if (prevData) {
    prevData.endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    saveAccessLogRecord(prevData);
    prevData = null;
  }

  const data = {
    id,
    permissionId,
    startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    endTime: '',
  };
  saveAccessLogRecord(data);
  prevData = data;
}

export function collectPrevLog() {
  if (prevData) {
    prevData.endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    saveAccessLogRecord(prevData);
    prevData = null;
  }
}

function recursiveConvert(node) {
  if (node.path) {
    const { children, ...rest } = node;
    routeObj[node.path] = rest;

    if (children && children.length > 0) {
      const routesObject: { [key: string]: any } = {};
      for (const route of children) {
        routesObject[route.path] = recursiveConvert(route);
      }
      routeObj[node.path].routes = routesObject;
    }
  }
}

function addWindowsEvents() {
  console.log('addWindowsEvents');
  window.addEventListener('beforeunload', () => {
    if (prevData) {
      prevData.endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
      saveSccessLogRecordByFetch(prevData);
      prevData = null;
    }
  });
}

function generate32BitString() {
  const timestamp = Date.now().toString();
  const randomNumber = Math.floor(1000000000000000000 + Math.random() * 9000000000000000000)
    .toString()
    .slice(-8);
  return `${timestamp}${randomNumber}`; // 返回 32 位长度字符串
}

function removeStartSubstring(str: string, eqv = '/ipms/') {
  const index = str.indexOf(eqv);

  if (index !== -1) {
    // 从找到的索引到字符串结束
    return str.substring(index);
  } else {
    return '';
  }
}
