/**
 *  微应用apps
 * @name: 微应用名称 - 具有唯一性
 * @url: 微应用入口.必选 - 通过该地址加载微应用，
 */

import WujieVue from 'wujie-vue3';
import { wujieChildren } from '../router/routes'

const { preloadApp:_preloadApp, bus  } = WujieVue;

// 加载中的子应用
const loadingList:string[] = [];

// 应用生命周期钩子
const lifecycles = {
  beforeLoad: (appWindow) => {
    const key = appWindow.__WUJIE.id;
    console.log(`${key} beforeLoad 生命周期`,loadingList)
    // 将key添加到加载中
    loadingList.indexOf(key) < 0 && loadingList.push(key);
  },
  beforeMount: (appWindow) => console.log(`${appWindow.__WUJIE.id} beforeMount 生命周期`,new Date()),
  afterMount: (appWindow) => {
    const key = appWindow.__WUJIE.id;
    // 从加载中删除
    loadingList.indexOf(key) >= 0 && loadingList.splice(loadingList.indexOf(key),1)
    // 存入到已加载完成
    loadedList.push(key);
    // 下一个加载的索引
    let nextIndex = sortKeys.indexOf(key) + 1;
    while(nextIndex < sortKeys.length && loadedList.indexOf(sortKeys[nextIndex]) >= 0){
      nextIndex++
    }
    if(nextIndex > 0 && nextIndex < sortKeys.length && !loadingList.length){
      preloadApp(nextIndex)
    }
    console.log(`${key} afterMount 生命周期`,loadingList)
  },
  beforeUnmount: (appWindow) => console.log(`${appWindow.__WUJIE.id} beforeUnmount 生命周期`,new Date()),
  afterUnmount: (appWindow) => console.log(`${appWindow.__WUJIE.id} afterUnmount 生命周期`,new Date()),
  activated: (appWindow) => {
    const key = appWindow.__WUJIE.id;
    // window.postMessage("child_activated",key)
    bus.$emit(key + "-activated")
    console.log(`${appWindow.__WUJIE.id} activated 生命周期-bus`)
  },
  deactivated: (appWindow) => {
    const key = appWindow.__WUJIE.id;
    bus.$emit(key + "-deactivated")
    console.log(`${appWindow.__WUJIE.id} deactivated 生命周期-bus`)
  },
  loadError: (url, e) => console.log(`${url} 加载失败`, e),
};

//子应用列表
const _apps: object[] = [];
for (const key in import.meta.env) {
  if (key.includes('VITE_APP_SUB_')) {
    const name = key.split('VITE_APP_SUB_')[1];
    const _u = import.meta.env[key];
    const u = /.*\/$/.test(_u) ? _u : _u + '/';
    const url = location.protocol + u + `?versionAndTime=${Date.now()}`
    console.log('sub-url',url);
    const obj = {
      name,
      alive: true, // 保活
      url,
      sync: true,
      ...lifecycles,
      fiber:true,
    };
    _apps.push(obj);
  }
}


// 子应用的路由
const childrenPath = wujieChildren.reduce((prev,cur,index)=>{
  if(cur.children && cur.children.length){
    return [...prev,...cur.children.map(i=>cur.path + '/' + i.path)]
  }else{
    return [...prev,cur.path]
  }
},[])
// 所有子应用的key
const keys = childrenPath.map(i=>i.replace(/^\/middle\/(.*)$/,'$1')).map(i=>i.replace(/\-/g,'_'));
// 当前是哪个子应用
const pathname = location.pathname.replace(/^\/middle\/(.*)$/,'$1').replace(/\-/g,'_')
// 当前子应用优先级最高
const currentItem = keys.includes(pathname) ? {pathname:Infinity}:{}
// 所有子应用的默认优先级
const defaultExecItems ={
  ipms:0,
  ipms_ly1_a:0, 
  ipms_ly1_b:0,
  ipms_ly1_c:0,
  ipms_ly5_c:0,
  system:1,
  qc_web:2,
  pdos_web:2,
  bgos_web:2,
  slot_health_manage_web:2,
  purify_web:2,
  anode_web:2,
  global_web:2,
  phase3_web: 2,
  ly3_qc_web: 2,
  ly3_slot_health_manage_web:2,
}
// 历史点击的优先级
const historyItem = JSON.parse(localStorage.getItem('wujie-child-sort') || JSON.stringify({}));
// 综合优先级
const item = {
  ...defaultExecItems,
  ...historyItem,
  ...currentItem
}
// 所有key经过优先级排序
const sortKeys = keys.sort((i,j)=>{
  if(!item.hasOwnProperty(i)){
    return 1
  }
  if(!item.hasOwnProperty(j)){
    return -1
  }
  return item[j] - item[i]
})
// 已加载的keys
const loadedList:string[] = [];

export const preloadApp = (index = 0)=>{
  const key = sortKeys[index];
  const app = _apps.filter((i:any)=>i.name === key)[0]
  // app && loadedList.indexOf(key) < 0 && _preloadApp({
  //   ...app,
  //   exec:true
  // } as any)
}

export const updateHistory = (key)=>{
  const historyItem = JSON.parse(localStorage.getItem('wujie-child-sort') || JSON.stringify({}));
  historyItem[key] = Number(historyItem[key] || 0) + 1;
  console.log(historyItem)
  localStorage.setItem('wujie-child-sort',JSON.stringify(historyItem))
}

export const apps = _apps;
