/** * @Name CommonApplications * @Description：门户首页-常用应用 * <AUTHOR> * @Email: <EMAIL> * @Date: 2023/9/4 17:06 *
@FilePath: src\views\dashboard\HomePage\components\CommonApplications.vue */
<script setup lang="ts">
  import { getListApi } from '/@/views/dashboard/HomePage/components/commonApplications/commonApplications.api';
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const currentGetList = {
    appType: 'E',
    appClass: '1',
    appReleaseFlag: 'Y',
    // userPermission: true,
    column: 'sortOrder',
    order: 'asc',
  };
  let currentState = ref<any>([]);
  const getAppList = async () => {
    appList.value = await getListApi(currentGetList);
  };

  onMounted(() => {
    getAppList();
  });

  // const appList = ref([
  //   {
  //     appClass: '2',
  //     appCode: 'sys1',
  //     appLogo: '//**************:9000/sys/temp/PDF图标_1693988936872.jpg',
  //     appName: '系统管理1',
  //     appReleaseFlag: 'Y',
  //     appReleaseTime: '2023-09-07 09:51:01',
  //     appRoute: '/sys1',
  //     appType: 'E',
  //     appVersion: 'v1.0.2',
  //     description: null,
  //     id: '1699683103516860418',
  //   },
  // ]);

  const appList = ref([]);
  const handleGoPage = (path: string) => {
    if (path.startsWith('http')) {
      window.open(path, '_blank');
    } else {
      let domain = '//' + window.location.host + import.meta.env.VITE_PUBLIC_PATH || '/';
      if (domain.endsWith('/') && path.startsWith('/')) {
        path = path.substring(1, path.length);
      }
      path = domain + path;
      window.open(path, '_blank');
    }
  };
</script>

<template>
  <a-card class="custom-card" title="第三方应用">
    <div id="components-grid-demo-playground">
      <a-row :gutter="[16, 16]">
        <a-col :span="6" v-for="(item, index) in appList" :key="index">
          <div class="app-item cursor-pointer" @click="handleGoPage(item.appRoute)">
            <div class="app-icon">
              <a-avatar :size="48" :src="item.appLogo" />
            </div>
            <div class="app-title">{{ item.appName }}</div>
          </div>
        </a-col>
      </a-row>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .app-item {
    .app-icon {
      text-align: center;
      margin-bottom: 10px;
    }

    .app-title {
      font-size: 12px;
      text-align: center;
    }
  }
</style>
