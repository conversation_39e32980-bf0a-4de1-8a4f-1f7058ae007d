<template>
  <span :class="`${prefixCls}- flex items-center `">
    <Icon v-if="getIcon && !_.startsWith(getIcon, 'http')" :icon="getIcon" :size="18" :class="`${prefixCls}-wrapper__icon mr-2`" />
    <div v-else class="image">
      <Image :src="getIcon || ''" :preview="false" :width="18" :class="`${prefixCls}-wrapper__icon`" />
    </div>
    {{ getI18nName }}
  </span>
</template>
<script lang="ts">
  import { computed, defineComponent } from 'vue';

  import Icon from '/@/components/Icon/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { contentProps } from '../props';
  import { Image } from 'ant-design-vue';
  import _ from 'lodash-es';

  const { t } = useI18n();

  export default defineComponent({
    name: 'MenuItemContent',
    components: {
      Icon,
      Image
    },
    props: contentProps,
    setup(props) {
      const { prefixCls } = useDesign('basic-menu-item-content');
      const getI18nName = computed(() => t(props.item?.name));
      const getIcon = computed(() => props.item?.icon);
      
      return {
        prefixCls,
        getI18nName,
        getIcon,
        _
      };
    },
  });
</script>

<style>
.image {
  margin-right: 0.5rem;
}
</style>
