<template>
  <div class="scheduleBox">
    <div class="tuxiang" v-if="userInfo.info.avatar">
      <img :src="userInfo.info.avatar" alt="" />
    </div>
    <div class="tuxiang" v-else>
      <img :src="userInfo.info.sex == 2 ? avatar_female : avatar_male" alt="" />
    </div>
    <div class="userInfo">
      <div class="name">{{ userInfo.info.realname }}</div>
      <div class="dec" :title="userInfo.info.orgCodeTxt">{{ userInfo.info.orgCodeTxt }}</div>
      <template v-if="userInfo.info.roleNames.length > 0">
        <div class="job">
          <a-tooltip>
            <template #title> {{ userInfo.info.roleNames.toString().split(',').join(' | ') }}</template>
            <span v-for="(item, index) in userInfo.info.roleNames.slice(0, 6)" :key="index">
              {{ item }}
            </span>
          </a-tooltip>
        </div>
      </template>
      <div class="smallDec">上次登录时间：{{ userInfo.info.lastLoginTime }}</div>
      <div class="smallDec">本周登录次数：{{ userInfo.info.weekLoginCount }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import avatar_female from '/@/assets/images/avatar_female.png';
  import avatar_male from '/@/assets/images/avatar_male.png';
  import { getUserInfo } from '../index.api';
  const userInfo = reactive({
    info: {
      roleNames: [],
    },
  });
  function getPersonalUserInfo() {
    getUserInfo({}).then((res) => {
      userInfo.info = res;
    });
  }
  onMounted(() => {
    getPersonalUserInfo();
  });
</script>
<style scoped lang="less">
  .scheduleBox {
    width: 100%;
    height: 224px;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .tuxiang {
      width: 182px;
      height: 182px;
      margin-right: 20px;
      background: rgba(210, 210, 210, 0.5);
      border-radius: 8px;
      img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }
    }
    .userInfo {
      width: calc(100% - 202px);
      height: 182px;
      .name {
        font-size: 24px;
        color: #000;
        line-height: 48px;
      }
      .dec {
        font-size: 16px;
        color: #000;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .job {
        width: 100%;
        // height: 38px;
        overflow: hidden;
        margin: 5px 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        span {
          display: inline-block;
          width: calc((100% - 30px) / 3);
          margin-right: 10px;
          cursor: pointer;
          background: rgba(22, 119, 255, 0.1);
          color: #1677ff;
          border-radius: 4px;
          padding: 2px 10px;
          margin-bottom: 3px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          &:last-child {
            margin-right: 0;
          }
        }
      }
      .smallDec {
        font-size: 14px;
        color: rgba(113, 115, 118, 1);
        line-height: 22px;
      }
    }
  }
</style>
