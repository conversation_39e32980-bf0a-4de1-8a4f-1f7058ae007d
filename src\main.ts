import 'uno.css';
import '/@/design/index.less';
// 注册图标
import 'virtual:svg-icons-register';
import AppComp from './App.vue';
import { createApp, App, unref } from 'vue';
import { initAppConfigStore } from '/@/logics/initAppConfig';
import { setupErrorHandle } from '/@/logics/error-handle';
import { router, setupRouter } from '/@/router';
import { setupRouterGuard } from '/@/router/guard';
import { setupStore } from '/@/store';
import { setupGlobDirectives } from '/@/directives';
import { setupI18n } from '/@/locales/setupI18n';
import { registerGlobComp } from '/@/components/registerGlobComp';
import { registerThirdComp } from '/@/settings/registerThirdComp';
import { useSso } from '/@/hooks/web/useSso';
// 注册online模块lib
import { registerPackages } from '/@/utils/monorepo/registerPackages';
// 无界子应用注册
import { registerApps } from '/@/wujie/index';
//store
import { useUserStore } from '/@/store/modules/user';
import { PageEnum } from './enums/pageEnum';
// import {getToken} from '/@/utils/auth';
// import md5 from "crypto-js/md5";
// import {connectWebSocket, onWebSocket} from '/@/hooks/web/useWebSocket';
import { useGlobSetting } from '@/hooks/setting';
import { useMessage } from '@/hooks/web/useMessage';
import { getVersion } from '@/api/common/api';

const { createInfoModal } = useMessage();
const userStore = useUserStore();
const glob = useGlobSetting();

let _version = -1;

// 在本地开发中引入的,以提高浏览器响应速度
if (import.meta.env.DEV) {
  import('ant-design-vue/dist/antd.less');
}

let app: App<Element>;

async function bootstrap(props = {}) {
  // 创建应用实例
  app = createApp(AppComp);

  // 多语言配置,异步情况:语言文件可以从服务器端获得
  await setupI18n(app);

  // 配置存储
  setupStore(app);

  // 初始化内部系统配置
  initAppConfigStore();

  // 注册外部模块路由(注册online模块lib)
  registerPackages(app);

  // 注册全局组件
  registerGlobComp(app);

  if (!window.location.pathname.includes(PageEnum.M_LOGIN_PATH)) {
    //CAS单点登录
    await useSso().ssoLogin();
  }

  // 配置路由
  setupRouter(app);

  // 路由保护
  setupRouterGuard(router);

  // 注册全局指令
  setupGlobDirectives(app);

  // 配置全局错误处理
  setupErrorHandle(app);

  // 注册第三方组件
  await registerThirdComp(app);

  // 当路由准备好时再执行挂载( https://next.router.vuejs.org/api/#isready)

  console.time('等待路由Ready');
  await router.isReady();
  console.timeEnd('等待路由Ready');

  // 注册无界子应用
  registerApps();

  // wujie
  if (window.__POWERED_BY_WUJIE__) {
    const { data } = props as any;

    userStore.setToken(data?.token);
  }

  // 挂载应用
  app.mount('#app', true);
  // console.log('🚀 ~ bootstrap ~ import.meta.env:');

  /**
   * 线上环境版本检测
   *
   */
  if (import.meta.env.MODE !== 'development') {
    loopVersion();
  }
}

function loopVersion() {
  getVersion().then((res) => {
    const version = Number(res.version) || 0;
    if (_version === -1) {
      _version = version;
      setTimeout(loopVersion, 5000);
    } else if (_version === version) {
      setTimeout(loopVersion, 5000);
    } else if (version > _version) {
      createInfoModal({
        iconType: 'info',
        title: '温馨提示',
        content: '系统已升级,请刷新后重试',
        okText: '刷新',
        onOk: async () => {
          // await useSso().ssoLoginOut();
          window.location.reload();
        },
      });
    }
  });
}

//
// function initWebSocket() {
//   let token = getToken();
//   //将登录token生成一个短的标识
//   let wsClientId = md5(token);
//   let userId = unref(userStore.getUserInfo).id + '_' + wsClientId;
//   // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
//   let url = glob.domainUrl?.replace('https://', 'wss://').replace('http://', 'ws://') + 'system/api/websocket/' + userId;
//   connectWebSocket(url);
//   onWebSocket(onWebSocketMessage);
// }
//
// function onWebSocketMessage(data) {
//   console.log("onWebSocketMessage", data);
//   if (data.msgId === "F0000") {
//     createInfoModal({
//       iconType: 'warning',
//       title: "温馨提示",
//       content: "应用版本已升级,请刷新后重试",
//       onOk: async () => {
//         await useSso().ssoLoginOut();
//       },
//     });
//   }
// }

/** wujie */
if (window.__POWERED_BY_WUJIE__) {
  window.__WUJIE_MOUNT = () => {
    console.log('子应用挂载');
    const props = window.$wujie?.props;
    bootstrap(props);
  };
  window.__WUJIE_UNMOUNT = () => {
    console.log('子应用卸载');
    app.unmount();
  };
  // module脚本异步加载，应用主动调用生命周期
  window.__WUJIE.mount();
} else {
  bootstrap({});
}
