/**
 * @Name noticeAnnouncementModel
 * @Description：通知公告
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 11:11
 * @FilePath: src\api\dashboard\model\noticeAnnouncementModel.ts
 */
import { BasicPageParams, BasicResult } from '/@/api/model/baseModel';

export type NoticesParams = BasicPageParams;

export interface NoticesListItem {
  readonly busId: string;
  busType: string;
  cancelTime: string;
  createBy: string;
  createTime: string;
  delFlag: string;
  dtTaskId: string;
  endTime: string;
  readonly id: string;
  msgAbstract: string;
  msgCategory: string;
  msgCategory_dictText: string;
  msgContent: string;
  msgType: string;
  msgType_dictText: string;
  openPage: string;
  openType: string;
  priority: string;
  priority_dictText: string;
  readFlag: string;
  readonly sendId: string;
  sendStatus: string;
  sendStatus_dictText: string;
  // 发布时间
  sendTime: string;
  sender: string;
  starFlag: string;
  startTime: string;
  tenantId: string;
  // 标题
  titile: string;
  updateBy: string;
  // 更新时间
  updateTime: string;
  userIds: string;
}

export type NoticesListGetResultModel = BasicResult<NoticesListItem>
