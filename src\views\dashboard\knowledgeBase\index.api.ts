import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
enum Api {
  filesAdd = '/sys/files/add',
  filesDelete = '/sys/files/delete',
  filesDeleteBatch = '/sys/files/deleteBatch',
  filesEdit = '/sys/files/edit',
  filesList = '/sys/files/list',
  filesQueryById = '/sys/files/queryById',
  filesAddSysFile = '/sys/files/addSysFile',
  filesUploadSysFile = '/sys/files/uploadSysFile',

  addFavo='/sys/files/addFavo',
  listFavo='/sys/files/listFavo',
  deleteFavo='/sys/files/deleteFavo',
}
// 知识库-文档管理-通过id删除
export const deleteFiles = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中文档',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.filesDelete, params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 知识库-文档管理-批量删除
 * @param params
 */
export const batchDeleteFiles = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中文档',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.filesDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 知识库-文档管理-保存或者更新
 * @param params
 */
export const saveOrUpdateFiles = (params, isUpdate) => {
  const url = isUpdate ? Api.filesEdit : Api.filesAddSysFile;
  return defHttp.post({ url: url, params });
};
export const filesDelete = (params) => {
  // 此处不再将id添加到URL路径中，而是将其作为查询参数
  const urlWithQuery = `${Api.filesDelete}?id=${params.id}`;
  // 使用带查询参数的URL进行delete请求
  return defHttp.delete({ url: urlWithQuery });
};
// 知识库-文档管理-分页列表查询
export const getFilesListApi = (params) => {
  return defHttp.get({ url: Api.filesList, params });
};
// 知识库-文档管理-通过id查询
export const getFilesQueryByIdApi = (params) => {
  return defHttp.get({ url: Api.filesQueryById, params });
};
// 添加收藏
export const addFavo = (params) => {
  return defHttp.post({ url: Api.addFavo, params });
};
// 收藏列表
export const listFavo = (params) => {
  return defHttp.get({ url: Api.listFavo, params });
};
// 移除收藏
// export const deleteFavo = (params) => {
//   return defHttp.delete({ url: Api.deleteFavo, params });
// };
// 此函数接受一个对象params，期望这个对象包含一个id属性
// 修改后的移除收藏函数
export const deleteFavo = (params) => {
  // 此处不再将id添加到URL路径中，而是将其作为查询参数
  const urlWithQuery = `${Api.deleteFavo}?id=${params.id}`;
  // 使用带查询参数的URL进行delete请求
  return defHttp.delete({ url: urlWithQuery });
};
/**
 * 导入api
 */
export const getFilesUploadSysFileUrl = Api.filesUploadSysFile;
/**
 * 导入api
 */
