import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

// const ipms: AppRouteModule = {
//   path: '/middle',
//   name: '电解铝智慧工厂',
//   component: LAYOUT,
//   redirect: '/middle/ipms',
//   children: [
//     {
//       path: '/middle/ipms',
//       name: '电解铝智慧工厂',
//       component: () => import('/@/wujie/middle/ipms.vue'),
//       meta: {
//         title: '电解铝智慧工厂',
//         hideBreadcrumb: true,
//         hideChildrenInMenu: true,
//       },
//     },
//   ],
// };
// export default ipms;

const routes: AppRouteModule[] = []

for (const key in import.meta.env) {
  if (key.includes('VITE_APP_SUB_ipms')) {
    const name = key.split('VITE_APP_SUB_')[1];
    
    const item: AppRouteModule = {
      path: `${name}`,
      name: `${name}`,
      component: () => import('/@/wujie/middle/ipms.vue'),
      meta: {
        title: '电解铝智慧工厂',
        hideBreadcrumb: true,
        hideChildrenInMenu: true,
      },
    }

    routes.push(item);
  }
}

const ipms = {
  path: '/middle',
  name: '电解铝智慧工厂',
  component: LAYOUT,
  redirect: '/middle/ipms',
  children: routes
};

console.log('ipms-route:', ipms)

export default ipms;