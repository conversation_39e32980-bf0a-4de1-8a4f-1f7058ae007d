/** * @Name NoticeAnnouncement * @Description：门户首页-通知公告 * <AUTHOR> * @Email: <EMAIL> * @Date: 2023/9/4 16:48 *
@FilePath: src\views\dashboard\HomePage\components\NoticeAnnouncement.vue */
<script setup lang="ts">
  import { NoticesListItem } from '/@/api/dashboard/model/noticeAnnouncementModel';
  import { getSysNoticesList } from '/@/api/dashboard/noticeAnnouncement';
  import { RightOutlined } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { func } from 'vue-types';
  import { useModal } from '/@/components/Modal';
  import DetailModal from './DetailModal.vue';
  const [registerModal, { openModal }] = useModal();
  const router = useRouter();
  const getNoticeList = async () => {
    const records = await getSysNoticesList({ pageSize: 8 });

    noticesList.value = records;
  };
  onMounted(() => {
    getNoticeList();
  });
  const noticesList = ref<any>([]);
  function handleRouter() {
    // let url = `${window.location.origin}/system/system/notice`;
    //
    // window.location.href = url;
    router.push({
      path: '/notice',
    });
  }

  // const [register, { openModal: openFrom }] = useModal();
  function openFrom(record) {
    openModal(true, {
      record,
      isUpdata: true,
    });
    // 更多跳转system
  }
</script>
<template>
  <a-card class="custom-card" title="通知公告">
    <template #extra> <a @click="handleRouter">更多</a> </template>
    <a-list item-layout="horizontal" :data-source="noticesList">
      <template #renderItem="{ item }">
        <a-list-item>
          <template #actions>
            <span class="text-color-[#4E5969]"> {{ dayjs(item.sendTime).format('YYYY-MM-DD') }}</span>
          </template>
          <a-list-item-meta>
            <template #title>
              <a @click="openFrom(item)">
                <a-tag style="color: #86909c; border-color: #c9cdd4">{{ item.msgCategory == '1' ? '公告' : '消息' }}</a-tag>
                <a-typography-text :style="{ width: '80%', color: 'inherit' }" :ellipsis="{ tooltip: item.titile }" :content="item.titile" />
              </a>
            </template>
          </a-list-item-meta>
        </a-list-item>
      </template>
    </a-list>
  </a-card>
  <!--              <a-empty />-->
  <DetailModal title="查看详情" @register="registerModal" />
</template>

<style scoped lang="less">
  :deep(.ant-list-item) {
    padding: 5px 0;
    border-bottom-width: 0;
    //background-color: #efefef;

    &:not(:last-child) {
      //margin-bottom: 10px;
    }

    .ant-list-item-meta-title {
      margin-bottom: 0;
    }
  }
</style>
