/** * @Name Schedule * @Description：门户首页-日程安排 * <AUTHOR> * @Email: <EMAIL> * @Date: 2023/9/5 14:11 * @FilePath:
src\views\dashboard\HomePage\components\Schedule.vue */
<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { onMounted, ref, unref } from 'vue';

  const weekNum = [
    {
      value: 1,
      label: '第一周',
    },
    {
      value: 2,
      label: '第二周',
    },
    {
      value: 3,
      label: '第三周',
    },
    {
      value: 4,
      label: '第四周',
    },
    {
      value: 5,
      label: '第五周',
    },
    {
      value: 6,
      label: '第六周',
    },
  ];
  const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  import { getSysScheduleList, getSysScheduleWeekDays, getWeekCountApi } from '/@/api/dashboard/schedule';
  import { WeekDaysItem } from '/@/api/dashboard/model/scheduleModel';
  import { useModal } from '/@/components/Modal';
  import useWeeksInYearMonth from '/@/hooks/web/useWeeksInYearMonth';
  import { getListApi, getStatisticsCountApi, getworkLogListApi } from '/@/views/dashboard/HomePage/components/schedule/schedule.api';
  import dayjs from 'dayjs';
  import { PlusOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
  import ScheduleModal from './ScheduleModal.vue';
  import { tabsType } from './schedule.data';
  import DetailModal from './DetailModal.vue';
  const router = useRouter();
  const [register, { openModal: openDetail }] = useModal();
  const [registerModal, { openModal }] = useModal();

  const iframeUrl = ref('');
  const currentMonths = ref<string>(dayjs().format('YYYY-MM'));
  const dayList = ref<WeekDaysItem[]>([]);
  const currentDay = ref<string>('');
  const { weeksNumber, currentWeeksNumber } = useWeeksInYearMonth(currentMonths);
  const currentWeek = ref(currentWeeksNumber.value);
  const statisticalData = ref({
    pendingApproval: 0,
    applyFor: 0,
    daySchedule: 0,
    weekSchedule: 0,
  });
  const options = ref(weekNum.slice(0, weeksNumber.value));
  let scheduleList = ref([]);
  let workLogList = ref([]);
  const activeKey = ref(tabsType[0].key);
  const getScheduleList = async () => {
    const res = await getListApi({
      // page: 1,
      // pageSize: 100,
      startTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 00:00:00`,
      endTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 23:59:59`,
    });
    scheduleList.value = [...res];
  };
  const getWorkLogList = async () => {
    const res = await getworkLogListApi({
      startTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 00:00:00`,
      endTime: `${dayjs(currentDay.value).format('YYYY-MM-DD')} 23:59:59`,
      self: true,
    });
    workLogList.value = [...res];
  };
  const getScheduleWeekDays = async () => {
    const res = await getSysScheduleWeekDays({
      year: dayjs(currentMonths.value).format('YYYY'),
      month: dayjs(currentMonths.value).format('M'),
      week: currentWeek.value,
    });
    dayList.value = res;
    currentDay.value =
      res.find(({ date }) => {
        return date === dayjs().format('YYYY-MM-DD');
      })?.date || res[0].date;
    getScheduleList();
    getWorkLogList();
  };
  const getWeekCount = async () => {
    const res = await getWeekCountApi();
    // statisticalData
  };

  const getStatisticsCount = async () => {
    const res = await getStatisticsCountApi({});
    statisticalData.value = res;
  };

  onMounted(() => {
    getScheduleWeekDays();
    getWeekCount();
    getStatisticsCount();
  });
  const handleCheckMonth = (type) => {
    currentWeek.value = 1;
    if (type === 'last') {
      currentMonths.value = dayjs(currentMonths.value).subtract(1, 'month').format('YYYY-MM');
    } else {
      currentMonths.value = dayjs(currentMonths.value).add(1, 'month').format('YYYY-MM');
    }
    options.value = [];
    for (let i = 0; i < weeksNumber.value; i++) {
      options.value.push(weekNum[i]);
    }
    getScheduleWeekDays();
  };

  const handleCheckDay = (date: string) => {
    currentDay.value = date;
    getScheduleList();
    getWorkLogList();
  };

  const handleAddSchedule = () => {
    openModal(true, {
      isUpdate: false,
      activeKey: unref(activeKey),
    });
  };

  const handleSuccess = () => {
    getScheduleWeekDays();
    getWeekCount();
    getStatisticsCount();
  };
  const handleSelectWeeks = (value: any) => {
    currentWeek.value = value;
    getScheduleWeekDays();
  };
  const handleToToday = () => {
    currentWeek.value = currentWeeksNumber.value;
    currentMonths.value = dayjs().format('YYYY-MM-DD');
    getScheduleWeekDays();
  };
  const handleGoDetails = (record) => {
    if (record?.link) {
      iframeUrl.value = record?.link;
      openDetail(true);
      // window.open(record?.link, '_blank');
      return null;
    }

    let time = unref(currentDay);
    if (unref(activeKey) === tabsType[0].key) {
      time = record?.sendTime;
    }
    time = dayjs(time).format('YYYY-MM-DD');
    router.push({
      path: `/calendarDetails/${time}`,
    });
  };
</script>

<template>
  <a-card class="custom-card" title="日程安排">
    <template #extra
      ><a @click="handleAddSchedule">
        <PlusOutlined style="font-size: 13px; margin-right: 5px" />
        添加日程</a
      ></template
    >
    <div>
      <a-row :gutter="16">
        <a-col :span="6">
          <div class="border-1 border-[#5470C6] md:p-12px 2xl:p-24px bg-[#EEF1F9] rounded-4px">
            <p class="text-14px">待我审批</p>
            <p class="text-24px font-bold mb-0">{{ statisticalData.pendingApproval }}</p>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="border-1 border-[#9EC97F] md:p-12px 2xl:p-24px bg-[#F5FAF2] rounded-4px">
            <p class="text-14px">我的申请 </p>
            <p class="text-24px font-bold mb-0">{{ statisticalData.applyFor }}</p>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="border-1 border-[#DE6E6A] md:p-12px 2xl:p-24px bg-[#FCF1F0] rounded-4px">
            <p class="text-14px">今日日程</p>
            <p class="text-24px font-bold mb-0">{{ statisticalData.daySchedule }}</p>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="border-1 border-[#F2C96B] md:p-12px 2xl:p-24px bg-[#FEFAF0] rounded-4px">
            <p class="text-14px">本周日程</p>
            <p class="text-24px font-bold mb-0">{{ statisticalData.weekSchedule }}</p>
          </div>
        </a-col>
      </a-row>
      <div class="bg-[#F7F8FA] rounded-4px">
        <div
          class="md:pl-12px md:pr-12px md:pt-20px md:pb-20px 2xl:p-24px bg-gradient-to-l mt-16px rounded-tr-4px rounded-tl-4px"
          style="background: linear-gradient(90deg, #2c3fe1 0%, #6dd9f6 100%)"
        >
          <div class="flex justify-between items-center">
            <div class="flex justify-center items-center">
              <div class="flex justify-center items-center text-color-[#ffffff]">
                <span class="text-24px cursor-pointer" @click="handleCheckMonth('last')">&blacktriangleleft;</span>
                <span class="text-20px">{{ dayjs(currentMonths).format('YYYY年M月') }}</span>
                <span class="text-24px cursor-pointer" @click="handleCheckMonth('next')">&blacktriangleright;</span>
              </div>
              <div>
                <a-button size="small" class="ml-20px" @click="handleToToday">今天</a-button>
              </div>
            </div>
            <a-select v-model:value="currentWeek" size="small" class="w-86px" :options="options" @select="handleSelectWeeks" />
          </div>
        </div>
        <div class="flex">
          <div v-for="(item, index) in weeks" :key="index" class="flex-1 text-center pt-16px pb-16px">{{ item }} </div>
        </div>
        <div class="flex">
          <div
            v-for="(item, index) in dayList"
            :key="index"
            class="flex-1 text-center cursor-pointer"
            style="border: 1px solid #e5e6eb"
            @click="handleCheckDay(item.date)"
          >
            <p class="mb-0px mt-10px flex justify-center">
              <span class="w-32px h-32px block leading-32px rounded-4px" :class="[currentDay === item.date ? 'bg-[#1677FF] text-[#ffffff]' : '']">{{
                item.dayOfMonth
              }}</span>
            </p>
            <p class="mb-0px pt-5px pb-10px">
              <a-badge
                style="display: flex; align-items: flex-start; justify-content: center; padding-left: 12px"
                class="flex"
                color="#2db7f5"
                v-if="item.existToDone.toString() === 'true'"
              />
              <span v-else class="h-4px block"></span>
            </p>
          </div>
        </div>
      </div>
      <a-tabs v-model:activeKey="activeKey" size="small">
        <a-tab-pane :tab="tabsType[0].tab" :key="tabsType[0].key">
          <div class="mt-10px pt-10px h-400px overflow-y-scroll">
            <a-timeline v-if="scheduleList.length > 0">
              <a-timeline-item v-for="(item, index) in scheduleList" :key="index">
                <div
                  :style="{ cursor: 'pointer' }"
                  @click="
                    () => {
                      handleGoDetails(item);
                    }
                  "
                >
                  <p class="text-13px text-gray-400">{{ item?.sendTime }}</p>
                  <div class="bg-[#F7F8FA] rounded-4px p-16px">
                    <p class="mb-6px">
                      <!--                    <a-badge color="#2db7f5" />-->
                      <a-tag color="orange">{{ item?.type_dictText || '日程' }}</a-tag>
                      <b>{{ item?.title }} </b>
                    </p>
                    <p class="text-gray-400 text-13px mb-0">
                      {{ item?.startTime?.split(' ')[0] }}{{ item?.endTime ? ` - ${item?.endTime?.split(' ')[0]}` : '' }}</p
                    >
                    <!-- <p>{{ item.item }}</p> -->
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
            <div v-else class="flex justify-center pt-80px">
              <span
                >今日暂无日程，<span class="text-color-[#1890ff] cursor-pointer" @click="handleAddSchedule"
                  >添加日程<PlusCircleOutlined style="margin-left: 5px" /></span
              ></span>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane :tab="tabsType[1].tab" :key="tabsType[1].key">
          <div class="mt-10px pt-10px h-400px overflow-y-scroll">
            <a-timeline v-if="workLogList.length > 0">
              <a-timeline-item v-for="(item, index) in workLogList" :key="index">
                <div
                  :style="{ cursor: 'pointer' }"
                  @click="
                    () => {
                      handleGoDetails(item);
                    }
                  "
                >
                  <p class="text-13px text-gray-400">{{ item.startTime }}</p>
                  <div class="bg-[#F7F8FA] rounded-4px">
                    <p class="mb-6px">
                      <b>{{ item.title }} </b>
                    </p>
                    <p class="text-gray-400 text-13px mb-0"> {{ item.content }}</p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
            <div v-else class="flex justify-center pt-80px">
              <span
                >今日暂无日志，<span class="text-color-[#1890ff] cursor-pointer" @click="handleAddSchedule"
                  >添加日志<PlusCircleOutlined style="margin-left: 5px" /></span
              ></span>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>

  <ScheduleModal @register="registerModal" @success="handleSuccess" />
  <DetailModal @register="register" :frameSrc="iframeUrl" />
</template>

<style scoped lang="less"></style>
