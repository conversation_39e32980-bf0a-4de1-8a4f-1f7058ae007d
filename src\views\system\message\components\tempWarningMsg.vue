<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { getDictItemsByCode } from '@/utils/dict';
  import { getList, confirmAll as confirmAllReq, typeCount, confirm as confirmReq } from '@/api/temp/alarmMessage';

  const options = ref([]);
  const selectedType = ref(-1);
  const countDict = ref({});
  const typeDict = ref({});
  const nomore = ref(false);
  const loading = ref(false);
  const pagination = {
    pageSize: 15,
    pageNo: 0,
  };
  const list = ref([]);
  onMounted(() => {
    initLevelDict();
    reload(true);
  });

  function reload(resetCount = false) {
    if(resetCount) {
      initTypes();
    }
    pagination.pageNo = 0;
    list.value = [];
    nomore.value = false;
    load();
  }

  function load() {
    if (nomore.value) return;
    loading.value = true;
    pagination.pageNo++;
    getList({
      self: true,
      ...pagination,
      confirmState: 0,
      expired: 0,
      alarmType: selectedType.value == -1 ? 0 : selectedType.value,
      recoveryState: 0,
    })
      .then((res) => {
        if (res.pages === res.current) {
          nomore.value = true;
        }
        res.records.map((item) => {
          list.value.push(item);
        });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function initTypes() {
    const tempOp = [{ label: '全部', value: -1 }];

    // temp_monitor_alarm_type
    getDictItemsByCode('temp_monitor_alarm_type').map((item) => {
      tempOp.push({
        label: item.label,
        value: item.value,
      });
    });
    options.value = tempOp;


    for(let i in countDict.value) {
      countDict.value[i] = 0;
    }

    typeCount({
      self: true,
      confirmState: 0,
      expired: 0,
      recoveryState: 0,
    }).then((res) => {
      let allCount = 0;
      res.map((item) => {
        allCount += item.count;
        countDict.value[item.alarmType] = item.count;
      });
      countDict.value[-1] = allCount;
    });
  }

  function initLevelDict() {
    getDictItemsByCode('temp_monitor_alarm_level').map((item) => {
      typeDict.value[item.value] = item.label;
    });
  }

  function changeType(type) {
    if (type != selectedType.value) {
      selectedType.value = type;
      reload();
    }
  }

  function confirm(data) {
    loading.value = true;
    confirmReq({
      confirmState: 1,
      id: data.id,
    })
      .then(() => {
        reload(true);
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function confirmAll() {
    loading.value = true;
    confirmAllReq({
      alarmType: selectedType.value == -1 ? 0 : selectedType.value,
    })
      .then(() => {
        reload(true);
      })
      .finally(() => {
        loading.value = false;
      });
  }
</script>

<template>
  <a-spin :spinning="loading">
    <div class="temp-warning-msg">
      <div class="group-box">
        <div class="menu-box">
          <template v-for="item in options">
            <a-button :type="selectedType == item.value ? 'primary' : 'default'" block class="op-box" @click="changeType(item.value)">
              {{ item.label }}({{ countDict[item.value] ?? '0' }})
            </a-button>
          </template>
        </div>
        <a-popconfirm title="确定要一键确认吗?" ok-text="确认" cancel-text="取消" @confirm="confirmAll()">
          <a-button type="primary" block :disabled="list.length == 0">一键确认</a-button>
        </a-popconfirm>
      </div>

      <div class="content-box">
        <a-empty style="margin-top: 130px" v-if="list.length == 0" />

        <template v-for="item in list">
          <div class="alarm-box">
            <a-row>
              <a-col :span="12">
                <div class="value-box">
                  <div class="label">槽号：</div>
                  <div class="value">{{ item.potCode ?? '-' }}</div>
                </div>
                <div class="value-box">
                  <div class="label"></div>
                  <div class="value">
                    <a-tag color="error">{{ item.alarmTypeName ?? '-' }}</a-tag>
                  </div>
                </div>
                <div class="value-box">
                  <div class="label">数值：</div>
                  <div class="value">{{ item.temp ? Number(item.temp).toFixed(2) : '-' }}</div>
                </div>
                <div class="value-box">
                  <div class="label"></div>
                  <div class="value">
                    <a-tag color="#f2f3f5" style="color: #2d3139" v-if="item.alarmLevel == 0">
                      {{ typeDict[item.alarmLevel] ?? '-' }}
                    </a-tag>
                    <a-tag color="#f53f3f" style="color: #fff" v-if="item.alarmLevel == 1">
                      {{ typeDict[item.alarmLevel] ?? '-' }}
                    </a-tag>
                    <a-tag color="#ff7d00" style="color: #fff" v-if="item.alarmLevel == 2">
                      {{ typeDict[item.alarmLevel] ?? '-' }}
                    </a-tag>
                    <a-tag color="#f7ba1e" style="color: #fff" v-if="item.alarmLevel == 3">
                      {{ typeDict[item.alarmLevel] ?? '-' }}
                    </a-tag>
                  </div>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="value-box">
                  <div class="label">测量点位：</div>
                  <div class="value">{{ item.pointNo ?? '-' }}</div>
                </div>
                <div class="value-box">
                  <div class="label">参考值：</div>
                  <div class="value">{{ item.referenceTemp ? Number(item.referenceTemp).toFixed(2) : '-' }}</div>
                </div>
                <div class="value-box">
                  <div class="label">首次触发：</div>
                  <div class="value">{{ item.createAlarmTime ?? '-' }}</div>
                </div>
                <div class="value-box">
                  <div class="label">更新时间：</div>
                  <div class="value">{{ item.updateTime ?? '-' }}</div>
                </div>
              </a-col>
            </a-row>
            <div class="footer">
              <a-popconfirm title="确定要确认该报警吗?" ok-text="确认" cancel-text="取消" @confirm="confirm(item)">
                <a-button type="primary">确认</a-button>
              </a-popconfirm>
            </div>
          </div>
        </template>

        <div class="more-box" v-if="list.length != 0">
          <div v-if="nomore" class="no-more">没有更多数据了</div>
          <a-button v-else @click="load">加载更多</a-button>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style scoped lang="less">
  .temp-warning-msg {
    display: flex;
    height: 520px;

    .group-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      width: 200px;
      padding-left: 3px;

      .op-box {
        margin-bottom: 5px;
      }
    }

    .content-box {
      margin-left: 10px;
      flex: 1;
      overflow: auto;

      .alarm-box {
        padding: 10px;
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        margin-bottom: 10px;

        .value-box {
          height: 35px;
          line-height: 35px;
          display: flex;
          justify-content: start;
          align-items: center;

          .label {
            width: 80px;
            text-align: right;
          }
        }

        .footer {
          text-align: right;
          padding: 10px 10px 0 10px;
          border-top: 1px solid #cdcdcd;
        }
      }
    }
  }

  .more-box {
    text-align: center;

    .no-more {
      color: #999;
    }
  }
</style>
