<!--
 * @Description: 
 * @Autor: yst
 * @Date: 2024-10-22 08:35:37
 * @LastEditors: yst
 * @LastEditTime: 2024-10-24 18:00:08
-->
<template>
  <a-spin small="small" :tip="wait" :spinning="spinning" />
  <a-dropdown style="position: absolute; z-index: 520" v-model:visible="visible" :trigger="['click']">
    <a class="ant-dropdown-link" @click.prevent v-if="titleShow">
      反馈栏
      <DownOutlined />
    </a>
    <template #overlay>
      <div class="content">
        <a-form ref="formRef" :label-col="{ span: 3 }" :model="formState" name="basic" autocomplete="off">
          <a-form-item name="type" label="方式" :rules="[{ required: true, message: '方式不能为空' }]">
            <a-switch v-model:checked="checked1" @change="checked2 = !checked1" /> 建议
            <a-switch style="margin-left: 15px" v-model:checked="checked2" @change="checked1 = !checked2" /> 问题
          </a-form-item>

          <a-form-item v-if="checked2" name="date" label="日期">
            <a-date-picker valueFormat="YYYY-MM-DD HH:mm:ss" show-time v-model:value="formState.date" />
          </a-form-item>

          <a-form-item name="img" label="图片">
            <div class="uploadImg" style="display: flex">
              <JImageUpload @preview-cancel="visible = true" @preview="visible = false" :fileMax="3" v-model:value="formState.picture" />
              <!-- <JImageUpload @click="screenshot" v-if="formState.picture.split(',').length < 3" disabled text="当前页面" /> -->
            </div>
          </a-form-item>

          <a-form-item name="descr" :rules="[{ required: true, message: '描述不能为空' }]" label="描述">
            <a-textarea style="height: 100px" v-model:value="formState.descr" showCount :maxlength="200" />
          </a-form-item>
        </a-form>
        <div class="btn">
          <a-button @click="showConfirm">取消</a-button>
          <a-button type="primary" style="margin-left: 10px" @click="submit">提交</a-button>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
  import { reactive, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';

  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { sendFeedback, uploadUrl } from '/@/api/common/api';
  import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';

  const { createMessage } = useMessage();
  const getCurrentFormattedDate = (): string => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const spinning = ref(false);
  const wait = ref('');
  const formRef = ref();
  const visible = ref(false);
  const titleShow = ref(true);

  const checked1 = ref(true);
  const checked2 = ref(false);
  const showConfirm = () => {
    visible.value = false;
    formState.type = 1;
    checked2.value = false;
    checked1.value = true;
    formState.date = getCurrentFormattedDate();
    formState.descr = '';
    formState.picture = '';
  };
  // 当前路由
  const route = useRoute();
  function reverseText(title) {
    const parts = title.split('-');
    if (parts.length === 2) {
      return parts.reverse().join('-');
    }
    return title;
  }

  const submit = async () => {
    const ele = document.getElementsByTagName('iframe');
    const item = Array.from(ele);
    console.log(item, 'ddd');
    let title = '';
    console.log(route, 'ddd');
    const fullPath = route.fullPath;
    console.log(fullPath, 'ddd');
    if (!fullPath.includes('middle')) {
      if (route.name == '统一门户') {
        title = `统一门户 - 首页`;
      } else {
        title = `统一门户 - ${route.name}`;
      }
      console.log(title, 'ddd');
    } else {
      const str = fullPath.split('/')[2];
      console.log(str, 'ddd');
      const questionMarkIndex = str.indexOf('?');
      console.log(questionMarkIndex, 'ddd');
      const result = questionMarkIndex > -1 ? str.substring(0, questionMarkIndex) : str;
      console.log(result, 'ddd');
      const resultString = result.split('-').join('_');
      console.log(resultString, 'ddd');
      item.forEach((v) => {
        if (v.name == resultString) {
          console.log(v, 'ddd');
          title = reverseText(v.contentDocument.title);
        }
      });
    }
    await formRef.value.validateFields();
    const params = {
      entrance: title,
      ...formState,
      type: checked2.value ? 2 : 1,
      date: checked2.value ? formState.date : '',
    };
    await sendFeedback(params);
    visible.value = false;
    formState.type = 1;
    checked2.value = false;
    checked1.value = true;
    formState.date = getCurrentFormattedDate();
    formState.descr = '';
    formState.picture = '';
  };

  const base64ToFile = (base64String, fileName = 'shoot.png', mimeType = 'image/png') => {
    const base64Data = base64String.replace(/^data:([A-Za-z-+\/]+);base64,/, '');
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });
    const file = new File([blob], fileName, { type: mimeType });
    return file;
  };

  const screenshot = async () => {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          mediaSource: 'screen',
          cursor: 'always',
        },
      });

      // Create a video element to play the captured stream
      const videoElement = document.createElement('video');
      videoElement.srcObject = stream;
      videoElement.play();

      // Once the video starts playing, capture a frame
      videoElement.onloadedmetadata = async () => {
        const canvas = document.createElement('canvas');
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        const context = canvas.getContext('2d');
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const dataURL = canvas.toDataURL();
        stream.getTracks().forEach((track) => track.stop());
        spinning.value = true;
        titleShow.value = false;
        wait.value = '正在截图...';
        visible.value = false;
        setTimeout(async () => {
          const blob = base64ToFile(dataURL);
          let params = {
            file: blob,
          };
          defHttp.uploadFile({ url: uploadUrl }, params, {
            success: (res) => {
              if (res.success) {
                formState.picture.length ? (formState.picture += `,${res.message}`) : (console.log('ddd222'), (formState.picture = `${res.message}`));
                visible.value = true;
                wait.value = '';
                spinning.value = false;
                titleShow.value = true;
              } else {
                titleShow.value = true;
                createMessage.error(res.message || '操作失败');
              }
            },
          });
        }, 200);
      };
    } catch (err) {
      console.error('Error capturing the webpage: ', err);
    }
  };

  const formState = reactive({
    type: 1,
    date: getCurrentFormattedDate(),
    descr: '',
    picture: '',
  });
  watch(
    () => formState.picture,
    () => {
      console.log(formState.picture, 'formState');
    }
  );
</script>
<style>
  .ant-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: tnum;
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 520;
    display: block;
  }
</style>

<style scoped lang="less">
  :deep(.ant-upload .ant-upload-disabled) {
    cursor: pointer;
  }
  :deep(.ant-dropdown) {
    z-index: 520 !important;
  }
  .content {
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    width: 600px;
    min-height: 200px;
    background-color: #fff;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    .btn {
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
