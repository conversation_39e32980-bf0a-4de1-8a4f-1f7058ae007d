<template>
  <div :class="[prefixCls, getLayoutContentMode]" v-loading="getOpenPageLoading && getPageLoading">
    <UpdatePassword
      :content="strengthLow ? '密码强度太低，请立即修改密码' : '密码已失效，请立即修改密码'"
      :maskClosable="false"
      :closable="false"
      :showCancelBtn="false"
      ref="updatePasswordRef"
      v-if="strengthLow || userStore.getExpire"
    />
    <!-- || userStore.getExpire -->
    <PageLayout />
    <!-- update-begin-author:zyf date:20211129 for:qiankun 挂载子应用盒子 -->
    <!--    <div id="content" class="app-view-box" v-if="openQianKun == 'true'"></div>-->
    <!-- update-end-author:zyf date:20211129 for: qiankun 挂载子应用盒子-->
  </div>
</template>
<script lang="ts">
  import { defineComponent, onMounted, ref, nextTick } from 'vue';
  import PageLayout from '/@/layouts/page/index.vue';
  import UpdatePassword from '/@/layouts/default/header/components/user-dropdown/UpdatePassword.vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
  import { useContentViewHeight } from './useContentViewHeight';
  // import registerApps from '/@/qiankun';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useUserStore } from '@/store/modules/user';
  import { getRefPromise } from '@/utils';
  const userStore = useUserStore();
  const updatePasswordRef = ref();

  export default defineComponent({
    name: 'LayoutContent',
    components: { PageLayout, UpdatePassword },
    async mounted() {
      let res = await getRefPromise(updatePasswordRef);
      updatePasswordRef.value.show(userStore.getUserInfo.username);
    },
    setup() {
      const strengthLow = ref();
      const { prefixCls } = useDesign('layout-content');
      const { getOpenPageLoading } = useTransitionSetting();
      const { getLayoutContentMode, getPageLoading } = useRootSetting();
      const globSetting = useGlobSetting();
      onMounted(async () => {
        const strengthLowValue = localStorage.getItem('passwordStrengthLow');
        if (strengthLowValue) {
          localStorage.removeItem('passwordStrengthLow');
          console.log('已清除密码强度缓存。');
        }
        // 如果 localStorage 中有值，则比较该值是否为 'true'，否则默认为 false
        strengthLow.value = strengthLowValue !== null ? strengthLowValue === 'true' : false;

        await nextTick(); // 确保所有UI更新完毕
        console.log('strengthLow的值是', strengthLow.value);
      });
      useContentViewHeight();
      return {
        onMounted,
        strengthLow,
        userStore,
        prefixCls,
        getOpenPageLoading,
        getLayoutContentMode,
        getPageLoading,
        updatePasswordRef,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-layout-content';

  .@{prefix-cls} {
    position: relative;
    flex: 1 1 auto;
    min-height: 0;

    &.fixed {
      width: 1200px;
      margin: 0 auto;
    }

    &-loading {
      position: absolute;
      top: 200px;
      z-index: @page-loading-z-index;
    }
  }
</style>
