<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="900px" :bodyStyle="bodyStyle" destroyOnClose>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { scheduleFormSchema, logFormSchema } from './calendarDetails.data';
  import { getDetailById, update } from './calendarDetails.api';
  import dayjs from 'dayjs';

  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isWorkLog = ref(true);
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, resetSchema }] = useForm({
    schemas: scheduleFormSchema,
    showActionButtonGroup: false,
  });

  const getDetail = async (record) => {
    let fieldsValue = null;
    try {
      const res = await getDetailById({ id: record?.id }, unref(isWorkLog));
      if (unref(isWorkLog)) {
        fieldsValue = {
          content: res.content,
        };
      } else {
        //中文年月日改成/
        let startTime = res?.startTime.split(' ')[0].replace(/[^\d]/g, '/');
        let endTime = res?.endTime.split(' ')[0].replace(/[^\d]/g, '/');
        fieldsValue = {
          dateTime: `${dayjs(new Date(startTime)).format('YYYY-MM-DD HH:mm:ss')}, ${dayjs(new Date(endTime)).format('YYYY-MM-DD HH:mm:ss')}`,
          item: res?.item,
        };
      }
      //表单赋值
      await setFieldsValue({
        id: res.id,
        title: res?.title,
        ...fieldsValue,
      });
    } catch (err) {}
  };

  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    isWorkLog.value = data?.record?.type === 2;
    if (data?.record?.type === 1) {
      resetSchema(scheduleFormSchema);
    } else if (data?.record?.type === 2) {
      resetSchema(logFormSchema);
    }
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      await getDetail(data?.record);
    }
  });
  const bodyStyle = {
    height: '400px',
  };
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));

  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      setModalProps({ confirmLoading: true });
      //提交表单
      const { dateTime, ...otherValues } = values;
      let params = {
        ...otherValues,
      };
      if (!unref(isWorkLog)) {
        let startTime = dateTime.split(',')[0];
        let endTime = dateTime.split(',')[1];
        params = {
          ...params,
          startTime: startTime,
          endTime: endTime,
        };
      }
      await update(params, unref(isWorkLog));
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style scoped></style>
