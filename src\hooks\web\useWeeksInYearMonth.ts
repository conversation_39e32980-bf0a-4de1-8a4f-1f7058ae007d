/**
 * @Name useWeeksInYearMonth
 * @param date.value 年月格式为'YYYY-MM' 不传则为当月
 * @Description：获取当前月份有几个周
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 15:58
 * @FilePath: src\hooks\web\useTimePeriod.ts
 */

import { ref, unref, watch } from 'vue';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear'; // 导入插件
dayjs.extend(weekOfYear) // 使用插件
/**
 * @Name useWeeksInYearMonth
 * @param date
 */
export default function useWeeksInYearMonth(date) {
  const weeksNumber = ref<number>(0);
  const currentWeeksNumber = ref<number>(1);

  function getWeeksNumber() {
    const month = Number(dayjs(date.value).format('M'));
    if (month === 1) {
      weeksNumber.value = dayjs(date.value).endOf('month').week() + 1
    } else {
      weeksNumber.value = dayjs(date.value).endOf('month').week() - dayjs(date.value).startOf('month').week() + 1
    }
  }

  function getCurrentWeeksNumber() {
    const today = dayjs();
    const isFirstDayOfMonth = today.isSame(dayjs().startOf('month'), 'day'); // first day of month
    const isJanuary = today.month() === 0; // 是不是1月
    if (isJanuary) {
      if (isFirstDayOfMonth) {
        currentWeeksNumber.value = 1
      } else {
        currentWeeksNumber.value = today.week() + 1
      }
    } else {
      currentWeeksNumber.value = today.week() - today.startOf('month').week() + 1
    }
  }

  watch(() => date, () => {
    console.log('useWeeksInYearMonth', date.value);
    getWeeksNumber();
  }); //
  getWeeksNumber();
  getCurrentWeeksNumber()
  return {
    weeksNumber,
    currentWeeksNumber
  };
}
