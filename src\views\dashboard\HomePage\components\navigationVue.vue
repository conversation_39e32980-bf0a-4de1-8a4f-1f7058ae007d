<template>
  <div class="navBox">
    <div class="smallTit" style="font-weight: bold">产品中心</div>
    <div class="navSmallBox">
      <a-row :gutter="[16, 16]">
        <a-col v-for="group in indexApps" :span="group.groupId === 1 ? 12 : 6" :key="group.groupId">
          <a-card>
            <template #title>
              <div class="navSmallBoxCardTitle">
                <img v-if="group.groupId === 1" src="/@/assets/portal/portal_product_core_icon.png"
                  :alt="group.groupName" />
                <img v-else-if="group.groupId === 2" src="/@/assets/portal/portal_product_quality_icon.png"
                  :alt="group.groupName" />
                <img v-else-if="group.groupId === 3" src="/@/assets/portal/portal_product_auxiliary_icon.png"
                  :alt="group.groupName" />
                <img v-else src="/@/assets/portal/portal_product_core_icon.png" :alt="group.groupName" />
                {{ group.groupName }}
              </div>
            </template>
            <a-row :gutter="[16, 16]">
              <a-col v-for="app in group.apps" :span="group.groupId === 1 ? 12 : 24" :key="app.id">
                <div class="navTreeSmallBox"
                  :class="{ quality: group.groupId === 2, auxiliary: group.groupId === 3, disabled: !app.permission }"
                  @click="navRoute(app.appRoute)">
                  <div class="navTreeSmallContent">
                    <div class="navTitSmall">{{ app.appName }}</div>
                    <div v-if="app.permission" class="ButEnter"> 立即进入 </div>
                  </div>
                  <div class="navImgSmall">
                    <img :src="app.appLogo" :alt="app.appName" />
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script setup lang="ts">
// 导入导航图标

import app_icon01 from '/@/assets/portal/app_icon01.png';
import nav01 from '/@/assets/images/homeImg/nav/nav01.png';
import nav01Disabled from '/@/assets/images/homeImg/nav/nav01_disabled.png';
import nav02 from '/@/assets/images/homeImg/nav/nav02.png';
import nav02Disabled from '/@/assets/images/homeImg/nav/nav02_disabled.png';
import nav03 from '/@/assets/images/homeImg/nav/nav03.png';
import nav03Disabled from '/@/assets/images/homeImg/nav/nav03_disabled.png';
import nav04 from '/@/assets/images/homeImg/nav/nav04.png';
import nav04Disabled from '/@/assets/images/homeImg/nav/nav04_disabled.png';
import nav05 from '/@/assets/images/homeImg/nav/nav05.png';
import nav05Disabled from '/@/assets/images/homeImg/nav/nav05_disabled.png';
import nav06 from '/@/assets/images/homeImg/nav/nav06.png';
import nav06Disabled from '/@/assets/images/homeImg/nav/nav06_disabled.png';
import nav07 from '/@/assets/images/homeImg/nav/nav07.png';
import nav07Disabled from '/@/assets/images/homeImg/nav/nav07_disabled.png';
import nav08 from '/@/assets/images/homeImg/nav/nav08.png';
import nav08Disabled from '/@/assets/images/homeImg/nav/nav08_disabled.png';

import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { usePermissionStore } from '/@/store/modules/permission';
import { indexShowApps } from '@/api/common/api';
import { onMounted, ref } from 'vue';
import { useUserStore } from '/@/store/modules/user';

const userStore = useUserStore();

const permissionStore = usePermissionStore();
const router = useRouter();

let isShowRouter;
let routerList = permissionStore.$state.backMenuList;
isShowRouter = routerList.some((o) => o.path == '/middle/ipms_ly5_c'); // 铝五三厂
function navRoute(item) {
  isShowRouter = routerList.some((o) => o.path == item);
  if (isShowRouter) {
    router.push(item);
  } else {
    message.warning('暂无权限访问，可联系管理员配置');
  }
}


// 定义根据当前页面协议，处理图片路径方法
const handleImgPath = (imgPath?: string) => {
  if (!imgPath) return '';
  const pageProtocol = window.location.protocol; // 获取当前页面协议
  const imageUrl = new URL(imgPath);
  const imageProtocol = imageUrl.protocol; // 获取图片协议
  if (pageProtocol === imageProtocol) {
    // 如果图片协议与当前页面协议一致，则直接返回图片路径
    return imgPath;
  } else {
    // 如果图片协议与当前页面协议不一致，则将图片协议替换为当前页面协议
    return imgPath.replace(imageProtocol, pageProtocol);
  }
};



const indexApps = ref([]);
onMounted(() => {
  indexShowApps({ corp: userStore.getTenant }).then((res) => {
    indexApps.value = res.map((item) => {
      let apps = item.apps;
      apps = apps.map((item) => {
        return {
          ...item,
          appLogo: handleImgPath(item.appLogo),
        }
      });
      if (item.groupId == 1) {
        // 移除所有“厂系列”应用，此类应用都有厂id字段‘factoryId’都不为空，整合为一个‘智慧电解工艺’应用，并将其放在apps数组的最前一个位置，如果存在‘厂系列’应用，‘智慧电解工艺’应用权限为true，否则为false；
        // 1. 获取所有‘厂系列’应用数量
        const factoryArr = apps.filter((app) => app.factoryId && app.permission);
        // 2. 移除所有‘厂系列’应用
        apps = apps.filter((app) => !app.factoryId);
        // 3. 将‘智慧电解工艺’应用添加到apps数组的最前一个位置,根据‘厂系列’应用数量设置‘智慧电解工艺’应用权限
        let factoryItem = {};
        if (factoryArr.length > 0) {
          const getFactoryId = userStore.getFactoryId || factoryArr[0].factoryId;
          userStore.setFactoryId(getFactoryId);
          factoryItem = factoryArr.find((item) => item.factoryId === getFactoryId) || factoryArr[0];
        }
        apps.unshift({
          appName: '智慧电解工艺',
          appLogo: app_icon01,
          appCode: 'ly-ipms', // 方便匹配使用，没有实际意义
          appRoute: factoryArr.length > 0 && factoryItem ? factoryItem.appRoute : '',
          permission: factoryArr.length > 0 && factoryItem.appRoute,
        });
      }
      return {
        ...item,
        apps,
      };
    });
  });
});

const handleNav = (data: Record<string, any>) => {
  const mapList = {
    'ly-ipms': {
      imgSrc: `url(${nav01})`,
      disabledSrc: `url(${nav01Disabled})`,
    },
    pdos_web: {
      imgSrc: `url(${nav02})`,
      disabledSrc: `url(${nav02Disabled})`,
    },
    slot_health_manage: {
      imgSrc: `url(${nav03})`,
      disabledSrc: `url(${nav03Disabled})`,
    },
    temp_monitor: {
      imgSrc: `url(${nav04})`,
      disabledSrc: `url(${nav04Disabled})`,
    },
    qc_web: {
      imgSrc: `url(${nav05})`,
      disabledSrc: `url(${nav05Disabled})`,
    },
    purify_web: {
      imgSrc: `url(${nav06})`,
      disabledSrc: `url(${nav06Disabled})`,
    },
    bgos_web: {
      imgSrc: `url(${nav07})`,
      disabledSrc: `url(${nav07Disabled})`,
    },
    anode_web: {
      imgSrc: `url(${nav08})`,
      disabledSrc: `url(${nav08Disabled})`,
    },
  };

  const item = mapList[data.appCode];
  return data.permission ? item.imgSrc : item.disabledSrc;
};
</script>
<style scoped lang="less">
.navBox {
  width: 100%;
  // height: 256px;
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px;
  border-radius: 8px;

  //margin-bottom: 16px;
  .smallTit {
    font-size: 22px;
    color: #000;
    line-height: 28px;
    margin-bottom: 20px;
  }

  .navSmallBox {
    width: 100%;
    // height: 260px;

    .navSmallBoxCardTitle {
      display: flex;
      align-items: center;

      &>img {
        width: 30px;
        height: 30px;
        margin-right: 10px;
      }
    }

    .navTreeBox {
      height: 160px;
      width: 100%;
      border-radius: 8px;
      background: linear-gradient(170deg, #dceaff 0%, #ffffff 100%);
      position: relative;
      cursor: pointer;

      .imgBox {
        width: 136px;
        height: 120px;
        position: absolute;
        right: 0;
        bottom: 0;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .titBox {
        position: absolute;
        width: calc(100% - 30px);
        height: 100%;
        box-sizing: border-box;
        padding: 20px 0 0 20px;
        z-index: 2;

        .tit {
          font-size: 22px;
          font-weight: bold;
          color: #0153d9;
          line-height: 22px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .smallTit {
          font-weight: normal;
          font-size: 16px;
          color: #8f97a3;
          line-height: 22px;
          margin-top: 15px;
        }

        .ButEnter {
          margin-bottom: 26px;
          background: rgba(22, 119, 255, 0.1);
          border-radius: 4px;
          box-sizing: border-box;
          font-size: 14px;
          line-height: 22px;
          color: #1d66de;
          text-align: center;
          padding: 2px 0;
          width: 88px;
          height: 31px;
          cursor: pointer;
        }
      }
    }

    //.fitBox {
    //  // margin-bottom: 20px;
    //  .navTreeSmallBox {
    //    width: 100%;
    //    height: 70px;
    //    background: rgba(220, 234, 255, 0.43);
    //    border-radius: 8px 8px 8px 8px;
    //    display: flex;
    //    justify-content: space-between;
    //    align-items: center;
    //    box-sizing: border-box;
    //    padding: 0 0 0 18px;
    //    cursor: pointer;
    //    .navImgSmall {
    //      width: 36px;
    //      height: 36px;
    //      margin-right: 10px;
    //      img {
    //        width: 100%;
    //        height: 100%;
    //      }
    //    }
    //    .navTitSmall {
    //      font-size: 18px;
    //      color: #000;
    //      line-height: 22px;
    //      text-overflow: ellipsis;
    //      overflow: hidden;
    //      white-space: nowrap;
    //    }
    //  }
    //}
    .navTreeSmallBox {
      width: 100%;
      height: 94px;
      //background: rgba(220, 234, 255, 0.43);
      background-image: url('/@/assets/portal/portal_product_core_bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      justify-content: space-between;
      //align-items: center;
      box-sizing: border-box;
      padding: 0 10px 0 18px;
      cursor: pointer;

      &.disabled {
        filter: grayscale(70%);
        opacity: 0.9;
      }

      .navImgSmall {
        width: 54px;
        height: 54px;
        min-width: 54px;
        /* 确保图标区域有最小宽度 */
        margin-right: 10px;
        align-self: center;
        flex-shrink: 0;

        /* 防止图标区域被压缩 */
        img {
          width: 100%;
          height: 100%;
        }
      }

      .navTreeSmallContent {
        padding-top: 15px;
        flex: 1;
        /* 让内容区域占据剩余空间 */
        overflow: hidden;
        /* 确保内容不会溢出 */
        margin-right: 10px;
        /* 与图标保持一定距离 */

        .navTitSmall {
          font-size: 20px;
          color: #ffffff;
          line-height: 22px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          margin-bottom: 14px;
          max-width: 100%;
          /* 确保文本不会超出容器 */
        }

        .ButEnter {
          color: #0973fb;
          background: #ffffff;
          width: 88px;
          height: 26px;
          line-height: 26px;
          font-size: 14px;
          border-radius: 150px;
          text-align: center;
          cursor: pointer;
        }
      }

      &.quality {
        background-image: url('/@/assets/portal/portal_product_quality_bg.png');

        .ButEnter {
          color: #15ae59;
        }
      }

      &.auxiliary {
        background-image: url('/@/assets/portal/portal_product_auxiliary_bg.png');

        .ButEnter {
          color: #fead3a;
        }
      }
    }

    .systemB {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      background-color: rgba(220, 234, 255, 0.43);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      color: #000;
      box-sizing: border-box;
      padding: 0 25px;
      cursor: pointer;
    }
  }

  @media (max-width: 1600px) {
    .navImgSmall {
      display: none !important;
    }
  }
}

::v-deep .ant-card-body {
  padding: 16px;
}
</style>
