/** * @Name CorporateNews * @Description：门户首页-企业新闻 * <AUTHOR> * @Email: <EMAIL> * @Date: 2023/9/4 16:15 * @FilePath:
src\views\dashboard\HomePage\components\CorporateNews.vue */
<script setup lang="ts">
  import { getSysNewsList } from '/@/api/dashboard/corporateNews';
  import { NewsListItem } from '/@/api/dashboard/model/corporateNewsModel';
  import { ref, onMounted } from 'vue';
  import { RightOutlined } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';

  const carouselRef = ref(null);
  const baseUrl = 'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/';
  const getImgUrl = (i: number) => {
    return `${baseUrl}abstract0${i + 1}.jpg`;
  };
  const text = `A dog is a type of domesticated animal.Known for its loyalty and faithfulness,it can be found as a welcome guest in many households across the world.`;
  const activeKey = ref(['0']);

  const getNewsList = async () => {
    const res = await getSysNewsList({
      page: 1,
      pageSize: 6,
    });
    newList.value = res?.records?.map((i: NewsListItem) => ({
      ...i,
      sendTime: dayjs(i.sendTime).format('YYYY-MM-DD').toString(),
    }));
  };

  onMounted(() => {
    getNewsList();
  });

  const newList = ref<NewsListItem | any[]>([]);

  const onCarouselChange = (current: string) => {
    activeKey.value = [current.toString()];
  };
</script>

<template>
  <a-card class="custom-card" title="企业新闻">
    <template #extra>
      <a class="text-color-[#86909C] text-size-14px" href="//www.weiqiaocy.com/cn/news.html" target="_blank">查看更多<right-outlined /></a>
    </template>
    <div class="corporateNews">
      <a-row :gutter="16">
        <a-col :span="11">
          <a-carousel arrows autoplay ref="carouselRef" dots-class="slick-dots slick-thumb" :after-change="onCarouselChange">
            <div v-for="(item, index) in newList" :key="index" style="position: relative">
              <img :src="item.picturePath" :alt="item.title" />
              <!--              <div-->
              <!--                style="position: absolute;bottom: 0;height: 40px;width:100%;background-color: #0a7be0">-->

              <!--              </div>-->
            </div>
          </a-carousel>
        </a-col>
        <a-col :span="13">
          <a-collapse collapsible="disabled" v-model:activeKey="activeKey" ghost accordion>
            <a-collapse-panel v-for="(item, index) in newList" :key="index">
              <template #header>
                <a
                  :href="item.link"
                  target="_blank"
                  class="text-color-[#1D2129] line-clamp-1 pr-5 font-bold"
                  :class="[activeKey.includes(index.toString()) ? 'text-16px' : ' text-14px']"
                >
                  {{ item.title }}</a
                >
              </template>
              <p class="line-clamp-2 mb-0 text-color-[#86909C] text-14px font-normal"> {{ item.descContent }}</p>
              <template v-if="!activeKey.includes(index.toString())" #extra>
                <div class="w-80px text-color-[#4E5969]">{{ item.sendTime }}</div>
              </template>
            </a-collapse-panel>
          </a-collapse>
        </a-col>
      </a-row>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .date-fade-enter-active,
  .date-fade-leave-active {
    transition: opacity 0.5s ease;
  }

  .date-fade-enter-from,
  .date-fade-leave-to {
    opacity: 0;
  }

  .slick-dots {
    bottom: 0 !important;
  }

  .corporateNews {
    :deep(.ant-collapse) {
      max-height: 320px;
      overflow: hidden;
    }

    :deep(div.ant-typography, .ant-typography p) {
      margin-bottom: 0;
    }

    :deep(.slick-slide img) {
      display: block;
      margin: auto;
      width: 100%;
      border-radius: 4px;
    }

    .active {
      font-size: 16px;
      font-weight: 500;
    }

    :deep(.ant-collapse-header) {
      padding: 10px 16px;

      & > div:not(.ant-collapse-extra) {
        display: none;
      }
    }

    .news-date {
      font-size: 12px;
      color: #aaaaaa;
      margin-bottom: 0;
    }

    :deep(.ant-collapse-item) {
      position: relative;

      &:after {
        content: ' ';
        display: block;
        position: absolute;
        bottom: 0;
        right: 0;
        left: 10px;
        height: 1px;
        background-color: #e0e0e0;
      }
    }

    :deep(.ant-collapse-content-box) {
      padding-top: 0 !important;
    }

    .content-box {
      padding-left: 10px;

      :deep(.ant-typography) {
        margin-bottom: 0;
        color: #aaaaaa;
        font-size: 14px;
        font-weight: 400;
      }
    }
  }
</style>
