<template>
  <div :class="`${prefixCls}-container`">
    <div style="color: #0973fb" v-show="isInSeriesApps">
      当前所在：
      <a-dropdown>
        <a @click.prevent>
          <Space>
            {{ currentFactoryName }}
            <DownOutlined />
            <a-icon type="down-outlined" />
          </Space>
        </a>
        <template #overlay>
          <a-menu v-model:selectedKeys="currentFactoryIds">
            <a-menu-item :disabled="it.permission && !it.appRoute" v-for="it in factoryOptions" :key="it.factoryId"
              @click="handleSelectFactory(it)">
              <a href="javascript:;">{{ it.appName }}</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { getCurrentUserFactoryApps } from '/@/api/common/api';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStore } from '/@/store/modules/user';
import { useRouter } from 'vue-router';
const router = useRouter();

const { prefixCls } = useDesign('factory-selector');
const userStore = useUserStore();

const currentFactoryName = ref('');
const currentFactoryIds = ref<any[]>([]);
// 是否在智慧工厂系列应用中
const isInSeriesApps = ref(false);

const factoryOptions = ref<any[]>([]); // 定义 factoryOptions 为 MenuProps['items'] 类型

onMounted(async () => {
  // 获取当前用户工厂列表
  const res = await getCurrentUserFactoryApps({ corp: userStore.getTenant });
  const departmentId = res.departmentId || userStore.getFactoryId || res.list[0].factoryId;
  userStore.setFactoryId(departmentId); // 更新当前工厂信息
  factoryOptions.value = res.list;
});

// 监听factoryOptions和路由变化，确保显示同步
watch(
  [factoryOptions, () => router.currentRoute.value.fullPath],
  ([options, path]) => {
    if (!options.length) return;
    const currentFactory = options.find(it => path.includes(it.appRoute));
    if (currentFactory) {
      currentFactoryName.value = currentFactory.appName;
      currentFactoryIds.value = currentFactory.factoryId ? [currentFactory.factoryId] : [];
      userStore.setFactoryId(currentFactory.factoryId);
      isInSeriesApps.value = true;
    } else {
      isInSeriesApps.value = false;
    }
  },
  { immediate: true }
);

const handleSelectFactory = async (it) => {
  currentFactoryName.value = it.appName;
  currentFactoryIds.value = it.factoryId ? [it.factoryId] : [];
  userStore.setFactoryId(it.factoryId); // 更新当前工厂信息
  await router.push({ path: it.appRoute });
  // 跳转后再次校准
  const currentFactory = factoryOptions.value.find(opt => it.appRoute.includes(opt.appRoute));
  if (currentFactory) {
    currentFactoryName.value = currentFactory.appName;
    currentFactoryIds.value = [currentFactory.factoryId];
  }
};
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-factory-selector';

.@{prefix-cls}-container {
  margin-right: 12px;

  :deep(.ant-select-selector) {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
  }

  :deep(.ant-select-arrow) {
    color: rgba(255, 255, 255, 0.8);
  }
}
</style>
