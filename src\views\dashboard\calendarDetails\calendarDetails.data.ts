import { BasicColumn, FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '标题',
    width: 150,
    dataIndex: 'title',
  },
  {
    title: '内容',
    width: 250,
    dataIndex: 'item',
  },
  {
    title: '日期',
    width: 160,
    dataIndex: 'date',
  },
  {
    title: '类型',
    width: 60,
    dataIndex: 'type_dictText',
  },
  {
    title: '更新时间',
    width: 100,
    dataIndex: 'updateTime',
  },
  // {
  //   title: '发布人',
  //   width: 100,
  //   dataIndex: 'applyForUserId',
  // },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'sendTime',
    label: '日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      placeholder: '请选择日期',
    },
    colProps: { span: 5 },
  },
  {
    field: 'search',
    label: '标题',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'type',
    label: '类型',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'schedule_log_type',
      placeholder: '请选择',
    },
    colProps: { span: 5 },
  },
];

export const scheduleFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '日程名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入日程名称',
    },
  },
  {
    field: 'dateTime',
    label: '时间',
    component: 'RangeDate',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['请选择开始日期时间', '请选择结束日期时间'],
    },

    dynamicRules: ({ model }) => rules.endTime(model.startTime, true),
  },
  {
    field: 'item',
    label: '日程内容',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      autoSize: { minRows: 11, maxRows: 50 },
      placeholder: '请输入日程内容(最多输入200个字符)',
      maxlength: 200,
    },
  },
];
export const detailFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '日程名称',
    component: 'Input',
  
    componentProps: {
      placeholder: '请输入日程名称',
    },
  },
  {
    field: 'dateTime',
    label: '时间',
    component: 'RangeDate',
   
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['请选择开始日期时间', '请选择结束日期时间'],
    },

    // dynamicRules: ({ model }) => rules.endTime(model.startTime, true),
  },
  {
    field: 'item',
    label: '日程内容',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入日程内容(最多输入200个字符)',
      autoSize: { minRows: 11, maxRows: 50 },
      maxlength: 200,
    },
  },
];
export const logFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '标题',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入日志标题',
    },
  },
  {
    field: 'content',
    label: '日志内容',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      autoSize: { minRows: 11, maxRows: 50 },
      placeholder: '请输入日志内容(最多输入200个字符)',
      maxlength: 200,
    },
  },
];
