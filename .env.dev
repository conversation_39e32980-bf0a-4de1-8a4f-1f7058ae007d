VITE_APP_ENV=dev

# 是否启用mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /saas-portal/

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

#后台接口父地址(必填)
VITE_GLOB_API_URL=/system/api

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=//ipms.dev.hongqiaocloud.com/

# 单点登录服务端地址
VITE_GLOB_APP_CAS_BASE_URL=//ipms.dev.hongqiaocloud.com/cas

# 文件预览地址
VITE_GLOB_ONLINE_VIEW_URL=//ipms.corp.hongqiaocloud.com/kkfile/onlinePreview

# 接口父路径前缀,您可以配置多个 ,请注意，没有换行符
# VITE_GLOB_API_URL_PREFIX=[["/portal/","system/api"],["/sys/","system/api"],["/system/","system/api"]]
VITE_GLOB_API_URL_PREFIX=[]

# 是否启用图像压缩
VITE_USE_IMAGEMIN= true

# 是否兼容旧浏览器
VITE_LEGACY = false

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_system = '//ipms.dev.hongqiaocloud.com/system'

VITE_APP_SUB_qc_web = '//ipms.dev.hongqiaocloud.com/qc-web'
VITE_APP_SUB_pdos_web = '//ipms.dev.hongqiaocloud.com/pdos-web'
VITE_APP_SUB_bgos_web = '//ipms.dev.hongqiaocloud.com/bgos-web'
VITE_APP_SUB_purify_web = '//ipms.dev.hongqiaocloud.com/purify-web'
VITE_APP_SUB_anode_web = '//ipms.dev.hongqiaocloud.com/anode-web/'
VITE_APP_SUB_global_web = '//ipms.dev.hongqiaocloud.com/global-web'
VITE_APP_SUB_slot_health_manage_web = '//ipms.dev.hongqiaocloud.com/slot-health-manage-web'
VITE_APP_SUB_phase3_web = '//ipms.dev.hongqiaocloud.com/ipms-phase3-web'
VITE_APP_SUB_temp_monitor_web = '//ipms.dev.hongqiaocloud.com/temp-monitor-web'
VITE_APP_SUB_ly3_slot_health_manage_web = '//ipms.dev.hongqiaocloud.com/ly3-slot-health-manage-web'
VITE_APP_SUB_ly3_qc_web = '//ipms.dev.hongqiaocloud.com/ly3-qc-web'

# VITE_APP_SUB_ipms = '//ipms.dev.hongqiaocloud.com/saas-ipms-web'
VITE_APP_SUB_ipms = '//ipms.dev.hongqiaocloud.com/ly1-a-ipms-web'
VITE_APP_SUB_ipms_ly1_a = '//ipms.dev.hongqiaocloud.com/ly1-a-ipms-web'
VITE_APP_SUB_ipms_ly1_b = '//ipms.dev.hongqiaocloud.com/ly1-b-ipms-web'
VITE_APP_SUB_ipms_ly1_c = '//ipms.dev.hongqiaocloud.com/ly1-c-ipms-web'
VITE_APP_SUB_ipms_ly5_c = '//ipms.dev.hongqiaocloud.com/ly5-c-ipms-web'
VITE_APP_SUB_ipms_ly3_c = '//ipms.dev.hongqiaocloud.com/ly3-c-ipms-web'
