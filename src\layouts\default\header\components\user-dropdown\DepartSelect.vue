<template>
  <BasicModal v-bind="config" :title="currTitle" v-model:visible="visible" wrapClassName="loginSelectModal">
    <a-form ref="formRef" v-bind="layout" :colon="false" class="loginSelectForm">
      <a-form-item :validate-status="validate_status">
        <!--label内容-->
        <template #label>
          <a-tooltip placement="topLeft">
            <template #title>
              <span>您隶属于多租户，请选择当前所属租户</span>
            </template>
            <a-avatar style="background-color: #87d068" :size="30"> 租户 </a-avatar>
          </a-tooltip>
        </template>
        <!--租户下拉内容-->
        <a-select v-model:value="tenantSelected" placeholder="请选择登录租户" :class="{ 'valid-error': validate_status == 'error' }">
          <template #suffixIcon>
            <Icon icon="ant-design:gold-outline" />
          </template>
          <template v-for="tenant in tenantList" :key="tenant.id">
            <a-select-option :value="(tenant as any).departmentId" :disabled="!tenant.permission">{{ (tenant as any).name }}</a-select-option>
          </template>
        </a-select>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="close">关闭</a-button>
      <a-button @click="handleSubmit" type="primary">确认</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { defineExpose, ref, unref, watch } from 'vue';
  import { BasicModal } from '/@/components/Modal';
  import { getUserDeparts, selectDepartment } from '/@/views/system/depart/depart.api';
  import { getUserTenants } from '/@/views/system/tenant/tenant.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';

  const userStore = useUserStore();
  const { createMessage, notification } = useMessage();
  const props = defineProps({
    title: { type: String, default: '部门选择' },
    closable: { type: Boolean, default: false },
    username: { type: String, default: '' },
  });

  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const config = {
    maskClosable: false,
    closable: false,
    canFullscreen: false,
    width: '500px',
    minHeight: 20,
    maxHeight: 20,
  };
  const currTitle = ref('切换租户');

  const isMultiTenant = ref(false);
  const currentTenantName = ref('');
  const tenantSelected = ref();
  const tenantList = ref([]);
  const validate_status = ref('');

  const isMultiDepart = ref(false);
  const currentDepartName = ref('');
  const departSelected = ref('');
  const departList = ref([]);
  const validate_status1 = ref('');
  //弹窗显隐
  const visible = ref(false);
  /**
   * 弹窗打开前处理
   */
  async function show() {
    //加载部门
    // await loadDepartList();
    //加载租户
    // await loadTenantList();

    //加载公司
    await loadCorpList();
    //标题配置
    // if (unref(isMultiTenant) && unref(isMultiDepart)) {
    //   currTitle.value = '切换租户和部门';
    // } else if (unref(isMultiTenant)) {
    //   currTitle.value =
    //     unref(currentTenantName) && unref(currentTenantName).length > 0 ? `租户切换（当前租户 :${unref(currentTenantName)}）` : props.title;
    // } else if (unref(isMultiDepart)) {
    //   currTitle.value =
    //     unref(currentDepartName) && unref(currentDepartName).length > 0 ? `部门切换（当前部门 :${unref(currentDepartName)}）` : props.title;
    // }
    //model显隐
    if (unref(isMultiTenant) || unref(isMultiDepart)) {
      visible.value = true;
    }
  }
  /**
   *加载部门信息
   */
  async function loadDepartList() {
    const result = await getUserDeparts();
    if (!result.list || result.list.length == 0) {
      return;
    }
    let currentDepart = result.list.filter((item) => item.orgCode == result.orgCode);
    departList.value = result.list;
    departSelected.value = currentDepart && currentDepart.length > 0 ? result.orgCode : '';
    currentDepartName.value = currentDepart && currentDepart.length > 0 ? currentDepart[0].departName : '';
    isMultiDepart.value = true;
  }
  /**
   *加载租户信息
   */
  async function loadTenantList() {
    const result = await getUserTenants();
    if (!result.list || result.list.length == 0) {
      return;
    }
    let tenantId = userStore.getTenant;
    let currentTenant = result.list.filter((item) => item.id == tenantId);
    currentTenantName.value = currentTenant && currentTenant.length > 0 ? currentTenant[0].name : '';
    tenantList.value = result.list;
    tenantSelected.value = tenantId;
    isMultiTenant.value = true;
  }

  /**
   *加载公司信息
   */
  async function loadCorpList() {
    // const result = await getUserTenants();
    const result = userStore.getUserCorp;
    console.log("🚀 ~ loadCorpList ~ result:", result)
    if (!result || result.length == 0) {
      return;
    }
    let tenantId = userStore.getTenant;
    let currentTenant = result.filter((item: any) => item.id == tenantId);
    currentTenantName.value = currentTenant && currentTenant.length > 0 ? (currentTenant[0] as any).name : '';
    tenantList.value = result.filter((item: any) => item.departmentId);
    tenantSelected.value = tenantId;
    isMultiTenant.value = true;
  }

  /**
   * 切换部门
   */
async function handleSubmit() {
    try {
      // 切换部门
      await selectDepartment({ departmentId: unref(tenantSelected), appCode: 'portal' });
      // 选中的公司id保存到store中
      userStore.setCorpId(unref(tenantSelected));
      userStore.setTenant(unref(tenantSelected));
      createMessage.success('切换成功');
      //切换租户后要刷新首页
      // 移除历史记录，跳转到门户首页
      window.location.href = '/';
      // window.location.reload();
    } catch (error) {
      console.log('切换部门失败', error);
    }
  }
  /**
   * 关闭model
   */
  function close() {
    departClear();
  }

  /**
   *初始化数据
   */
  function departClear() {
    // currTitle.value = '';

    isMultiTenant.value = false;
    currentTenantName.value = '';
    tenantSelected.value = '';
    tenantList.value = [];
    validate_status.value = '';

    isMultiDepart.value = false;
    currentDepartName.value = '';
    departSelected.value = '';
    departList.value = [];
    validate_status1.value = '';

    visible.value = false;
  }

  /**
   * 监听username
   */
  // watch(
  //   () => props.username,
  //   (value) => {
  //     value && loadDepartList();
  //   }
  // );

  defineExpose({
    show,
  });
</script>
<style lang="less" scoped>
  .loginSelectForm {
    margin-bottom: -20px;
  }

  .loginSelectModal {
    top: 20px;
  }

  .valid-error .ant-select-selection__placeholder {
    color: #f5222d;
  }
</style>
