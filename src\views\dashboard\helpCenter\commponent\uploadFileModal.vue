<template>
  <div>
    <BasicModal v-bind="$attrs" @register="register" :title="title" :width="600" @cancel="handleClose" :confirmLoading="uploading" destroyOnClose>
      <a-upload-dragger
          name="file"
          :multiple="true"
          :accept="updataAccept"
          :fileList="fileList"
          :remove="handleRemove"
          :beforeUpload="beforeUpload"
      >
        <p class="ant-upload-drag-icon">
          <Icon icon="ant-design:inbox-outlined" size="60" />
        </p>
        <p class="ant-upload-text">拖拽或点击这里上传</p>
      </a-upload-dragger>
      <slot name="content"></slot>
      <!--页脚-->
      <template #footer>
        <a-button @click="handleClose">关闭</a-button>
        <a-button type="primary" @click="handleImport" :disabled="uploadDisabled" :loading="uploading">{{
          uploading ? '上传中...' : '开始上传'
        }}</a-button>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, unref, watchEffect, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { defHttp } from '/@/utils/http/axios';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useMessage } from '/@/hooks/web/useMessage';

  export default defineComponent({
    name: 'JImportModal',
    components: {
      BasicModal,
    },
    props: {
      title: {
        type: String,
        default: '导入文件',
      },
      url: {
        type: String,
        default: '',
        required: false,
      },
      biz: {
        type: String,
        default: '',
        required: false,
      },
      //上传接口setting
      uploadSetting: {
        type: Object,
        default: {
          headers: {
            type: Object,
            default: {},
            required: false,
          },
          params: {
            type: Object,
            default: {},
            required: false,
          },
        },
        required: false,
        updataAccept: String,
        filesParamsName: String,
      },
    },
    emits: ['ok', 'register'],
    setup(props, { emit, refs }) {
      const { createMessage, createWarningModal } = useMessage();
      //注册弹框
      const [register, { closeModal }] = useModalInner((data) => {
        reset(data);
      });
      const glob = useGlobSetting();
      const attrs = useAttrs();
      const uploading = ref(false);
      //文件集合
      const fileList = ref([]);
      //上传url
      const uploadAction = ref('');
      const foreignKeys = ref('');
      //校验状态
      const validateStatus = ref(0);
      const updataAccept = ref(props?.uploadSetting?.updataAccept || '.xls,.xlsx');
      const getBindValue = Object.assign({}, unref(props), unref(attrs));
      //监听url
      watchEffect(() => {
        props.url && (uploadAction.value = `${glob.uploadUrl}${props.url}`);
      });
      //按钮disabled状态
      const uploadDisabled = computed(() => !(unref(fileList).length > 0));

      //关闭方法
      function handleClose() {
        closeModal() && reset();
        fileList.value = []; // 清空文件列表
      }

      //移除上传文件
      function handleRemove(file) {
        const index = unref(fileList).indexOf(file);
        const newFileList = unref(fileList).slice();
        newFileList.splice(index, 1);
        fileList.value = newFileList;
      }

      //上传前处理
      function beforeUpload(file) {
        fileList.value = [...unref(fileList), file];
        return false;
      }
      //文件上传
      function handleImport() {
        let { biz, online, uploadSetting } = props;
        const formData = new FormData();
        if (biz) {
          formData.append('isSingleTableImport', biz);
        }
        if (unref(foreignKeys) && unref(foreignKeys).length > 0) {
          formData.append('foreignKeys', unref(foreignKeys));
        }
        if (!!online) {
          formData.append('validateStatus', unref(validateStatus));
        }
        Object.keys(uploadSetting?.params).map((param) => {
          let value = uploadSetting?.params[param];
          formData.append(param, value);
        });
        unref(fileList).forEach((file) => {
          const filesParamsName = uploadSetting?.filesParamsName || 'files[]';
          formData.append(filesParamsName, file);
        });
        uploading.value = true;

        //TODO 请求怎样处理的问题
        let headers = {
          'Content-Type': 'multipart/form-data;boundary = ' + new Date().getTime(),
          ...uploadSetting.headers,
        };
        defHttp
          .post({ url: props.url, params: formData, headers }, { isTransformResponse: false })
          .then((res) => {
            uploading.value = false;
            if (res.success) {
              if (res.code == 201) {
                errorTip(res.message, res.result);
              } else {
                // createMessage.success(res.message);
              }
              handleClose();
              reset();
              emit('ok', res.result);
            } else {
              createMessage.warning(res.message);
            }
          })
          .catch((err) => {
            uploading.value = false;
          });
      }

      //错误信息提示
      function errorTip(tipMessage, fileUrl) {
        let href = glob.uploadUrl + fileUrl;
        createWarningModal({
          title: '导入成功,但是有错误数据!',
          centered: false,
          content: `<div>
                        <span>${tipMessage}</span><br/>
                        <span>具体详情请<a href = ${href} target="_blank"> 点击下载 </a> </span>
                      </div>`,
        });
      }

      //重置
      function reset(arg?) {
        fileList.value = [];
        uploading.value = false;
        foreignKeys.value = arg;
        validateStatus.value = 0;
      }

      return {
        updataAccept,
        register,
        getBindValue,
        uploadDisabled,
        fileList,
        uploading,
        validateStatus,
        handleClose,
        handleRemove,
        beforeUpload,
        handleImport,
      };
    },
  });
</script>
