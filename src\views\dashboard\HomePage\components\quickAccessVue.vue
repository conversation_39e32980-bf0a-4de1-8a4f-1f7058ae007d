<template>
  <div class="navBox">
    <div class="smallTit">快捷访问</div>
    <div class="navSmallBox">
      <div class="listIconLink" v-for="(item, index) in linkList.list" :key="index" @click="handleLink(item)">
        <div class="icon">
          <img :src="item.icon" alt="" />
        </div>
        <div class="nameIcon">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import nav01 from '/@/assets/images/homeImg/nav01.png';
  import nav02 from '/@/assets/images/homeImg/nav02.png';
  import nav03 from '/@/assets/images/homeImg/nav03.png';
  import nav04 from '/@/assets/images/homeImg/nav04.png';
  import nav05 from '/@/assets/images/homeImg/nav05.png';
  import logo01 from '/@/assets/images/homeImg/logo01.png';
  import logo02 from '/@/assets/images/homeImg/logo02.png';
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const linkList = reactive({
    list: [
      {
        icon: logo01,
        title: '集团首页',
        link: 'http://www.weiqiaocy.com/cn/',
      },
      {
        icon: logo02,
        title: '有色金属网',
        link: '//www.chinania.org.cn/',
      },
      //   {
      //     icon: nav03,
      //     title: '调度系统',
      //     link: '/portal',
      //   },
      //   {
      //     icon: nav04,
      //     title: '指标系统',
      //     link: '/portal',
      //   },
      //   {
      //     icon: nav05,
      //     title: '办公OA',
      //     link: '/portal',
      //   },
    ],
  });
  function handleLink(item) {
    window.open(item.link);
    // router.push(item.link);
  }
</script>
<style scoped lang="less">
  .navBox {
    width: 100%;
    height: 190px;
    background-color: #fff;
    box-sizing: border-box;
    padding: 20px;
    border-radius: 8px;
    .smallTit {
      font-size: 22px;
      color: #000;
      line-height: 28px;
      margin-bottom: 20px;
    }
    .navSmallBox {
      width: 100%;
      height: 100px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .listIconLink {
        height: 100px;
        margin: 0 10px;
        cursor: pointer;
        .icon {
          width: 56px;
          height: 56px;
          margin: 0 auto;
          border-radius: 8px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
          }
        }
        .nameIcon {
          width: 100%;
          height: 44px;
          font-size: 20px;
          text-align: center;
          color: #333;
        }
      }
    }
  }
</style>
